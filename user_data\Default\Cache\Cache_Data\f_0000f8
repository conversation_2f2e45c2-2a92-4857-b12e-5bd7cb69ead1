{"inputPlugins": {"DefaultPlugin": null, "RegexExtract": [{"rules": {"SearchImage": ["^https?://(?:www[.]|cn[.]|)bing.com/images/search[^#]*?[?&]q=([^&#]+)", "^https?://(?:www[.]|)google.(?:com|[a-z]{2}|(?:com|co)[.][a-z]{2})/search[^#]*?[?&]q=([^&#]+).*?&tbm=isch"], "SearchVideo": ["^https?://(?:www[.]|cn[.]|)bing.com/videos/search[^#]*?[?&]q=([^&#]+)", "^https?://(?:www[.]|)google.(?:com|[a-z]{2}|(?:com|co)[.][a-z]{2})/search[^#]*?[?&]q=([^&#]+).*?&tbm=vid"], "SearchWeb": ["^https?://(?:www[.]|cn[.]|)bing.com/search[^#]*?[?&]q=([^&#]+)", "^https?://(?:www[.]|)google.(?:com|[a-z]{2}|(?:com|co)[.][a-z]{2})/search[^#]*?[?&]q=([^&#]+)"]}, "preFilter": {"hostPrefix": ["www.bing.", "cn.bing.", "google.", "www.google."], "hostSuffix": [], "hostSubstring": []}}]}, "signals": [{"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 3990, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "Is1PGameDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "QHcgTPM5J8jCuY8lMK43I1k6x3ku6GcWYYk4A3xW+cNj4GGMh0RmLwyix0AEeEwfUcJTcH5QIT/BNGIhVVgRpJp6ITRXhpcE0C+OX87y22T3m1SmjvicJ5dzpMyD1bbhm8FaOqwV2L3OAIAbQ56/QUhmk5/DmRVbg2NQDUUJUHpBVPN2K2hGQwzrR5lvRkFNspKxuVSs9VVEyEY6rtO3QlEqQjl2EiZ55Ux7IxVmjMBftXV7TTYtkpYT+q0W1q65X2qsnVk0+hKH8VDSMoYvsaVytpSG0q4HuKMy10DS4/6jM67wRztQyHZCBxN32Di1dsx/lmnQh8lxRXMvWXVMYBQHiTIGLJLT93ARb6dMf5C41uBiKbkBJkyZfhBIye3UtZqTmUsIsCVpH1LhzaZlm8ia+VMP51qVDkVQDGRZov7+RH7SenITf1pn9d8qkWMUqy4IEIjTPReMBBEoCh1sbFtUQVWfAtLV2JODcL6azgvEFGV8aa/H96IttNFyuz0XbCJcJE41FB+rYdWiA6xRM1h+GNUjCnxXdRfFp+J12yRMrjdvmU/TwPhGElLpwaetsTvo6jdnyUYnoZVCcqrCVDnKPlG6os+UQX5mJK+kXf5y3K14BTSiJ+fbV9zF7MVUZryvZUEIsU70FOyG+pSf++Z4Kg==", "arraySize": 3990, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 2986, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "Is3PGameDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "8B/UM4NbHOqAkxq/KzP8t6DWP4O2uLhYbT3fHvqnabVInz8BsrySkCMdZa/bA0E6gpWhtDgaZ/1CvkccnbQpiYOvB+JQiXLuxpoJ2hqEOiIuoryCMYZftTo9vehJiLhEiJFois4h+epOK8sYMelhlx8BAjYT+Db8nEPH1hyG9VFO08pEtPiJEXikyLgyzYCp1jSHIPgqobhJ8utSFBD5ISC1qw5MCjhFRVuIpoePrI8zhReiAASItK+YmWlco4jtRhiz1XaGxoKAlrn+nKqeIexriYGZ5IK0VKnDDiRIJ+WhtFt+EhxqASioYlYRh/XvfGzO8ds5Hp4DtxVa4CglIc1wa7Yi/DQPhmRy/MCmRgZNi4goH7M/TgsDvjwDIvCBjWteXeAElXPJWQ8kq0on4UA7LpvgGruiaKdmsEbIkIBAApdCMOV/jCQB4L5Ko9NfS1wZp1fvBD5jiPftp6fmCaaaUAIqYmDgjAPOmFjBLdrG7YJo+gA=", "arraySize": 2986, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 991, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsTopWgiGames", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Url"], "parameters": {"base64BitVector": "cg33IZXvIB9NsolYdxH5xOuzYhn6mBFKwqd/S4w/0cai8cv4r0MdFHLO+au7z5nPGhBVttsPJl6Eshu0tdARJMMgEgVnpJKF5aBobAt+AQgem3rnSagTNnW0PJQzbhWdUu4PuWniGOJviqkoq3fGHCbIueCVuFLB1gSjbQ==", "arraySize": 991, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 6783, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsPrismExclusionDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "CHbcFpWCO1bMYjfwMGkFSdDJVffuW9ABTTKvHYos65eVUtqMuKgaEuAkMdiBBpcWfyU6QYisl5fV5ADPUTYR9UQG8BUmQlCKWMgSAD6+P57BlRriVGIr4IBBz53GGmh8WjJxHxjBhU/5s1dQ7F4tpcMzsjK9FHoj40D3Wp33/+xwpYwcnIVQJDwKb2Z4NhxO+DsBWREj1bJAJoZ8qsGrl/2vBadkKT4+/miQBIuml23NllR1q55OSONBLhAhvhV99xU5xDRD3wXRWMGWR703KRrhn+vMaUZ0O6vf+NB4PMgaLVTpLF5b2mV3xcsSf/l4VypkYIYmDsrStLJmX2HEv5/jxmccfBsWROjohgANMWDDQk+bZ6iM6wmFMXmbLKSwc8KEVIYZIB5ULYTc01evljfup8DAgHReGeAN7sdpymzqq6e9Y7DPzHQpAn3L5Q4AmKXqNKg/+j5BB0aVEIEXV4TlosKtyS1c60yyNrbfds0riLjVmnvSYdhFCNJH2gkoriAFa/w6n2tGqTCn45pDpLLg05ctKJM6pjucaLqd39IZmFwV9ARpqitkeVPWOivssB8+gHWiPj2hBwBkyUNbflG0Jur+qJ9A/FArgWG6/TkUbUVv0XuOYQC0LWFe+2OzuBJJ2aV9mtPyNhKStHfrshYZMA7GQQ/92oSDoe8AuDZHsK4IVIApQ/W4/oK2H4JqWj4AjbXPWxk9hZEnWMg+sOi0XoINzP9cZubBcw+QQgfDSSFLjxg37FWGf+ZMgF/lMNkWmMcA2sZm4UYMw54y2u8kp/B4EC406B7nQ0heRp+RPUNasbBag5rDAUTyPDwI79rF5rVGRFAFZpwOkaC/54OhDHFwasSDcgueE6qR2NshCBbt/cqO5bOecXWZJ+WwUHuqTFMtcQzgPGyhInF9ikXGFVHlwO5y0iZa3eKR5/ibHfLjpbMl0rElU8qHNBysKrM6xpV+y+bKJds1tRRqMRGXWQ5cfKcsMC9LcWH7bC3tE9GSFtf7+EIgEnXskdFLDCOpBjMrvDl8BtOY1W71asYlYd503cNruuoERsS7lDQYYOTAQl9COhapgzwqsjIzpWfjoJKMTUmbvOhgpBVdURqXB2uaeiNN45Jt3RFV9WY=", "arraySize": 6783, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 48, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsPrismExclusionSubdomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Subdomain"], "parameters": {"base64BitVector": "QUEFRUVF", "arraySize": 48, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 199198, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsRecip<PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Subdomain"], "parameters": {"base64BitVector": "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", "arraySize": 199198, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 2708, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsLocalQueryDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "5aVS2Nh36+El5QwXXVya17B1Qp1aQAzu61rceqpcdwdxP1ekBGv6Mf2iwtZZYJ06gk4fIegECQzWxZAkp97s23dC7IOyhTYUdY0SyV3MVNnDiuy0TT3ClCYJjKp+4h20S3nC0V4SVKSBN7HubAjNccr1/3XEHGUV3k5+CCOVEUn0KuAHFG4Qfq0BYU/L5G2Ba3E4JHaHikW0WP/B1fSmjCFhWMb4FwlrLoBzw3HVN+Iz/j6VX04p60GMlIVikketGCQyLfEVaAkrM59EGeiKDWTaop9FAs412kXUPeKyGVTTSVbdrG5565tASFjiTEsORxwdLcYyDDbeoRkZFi2bGEfGHkk3IoO8g3Ip9LrNeq2OmG1CuodkfeussguGplsCtvDSOMIJ1Z+XZRTUivvB5OKBBK+Q8SQtg1EjMy926Wy1Rlav4ZDUYJRD3GxUecnAhJ4E", "arraySize": 2708, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 350, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsLocalQuerySubdomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Subdomain"], "parameters": {"base64BitVector": "siKLT3AYQRgMXe3dTV50xaQBGNG1mdAyXQ9DHflZMQJ6S3UzqadTcIX8rxM=", "arraySize": 350, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 2587, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsFinanceQueryDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "qje0sZRySPloma3ppygABCa4AcM6y8SR4q14B9l1a1jDHwYSk+bS64kvGCIGkRt+PZtMJe35Y6lkF95CA8dommFIQPhsHKvQS7+HI7pxGKJ84+IedGQ88ln3B+qqk45EaiRE8g5l/PwjJl203qfyQ6tURzLsZL6Xy38Zt/kVMrcqAhjfzNKXVnipSHWtOTPSfmrHHgT0FsFdQBHbqeUOm3ASzmZDDCxVnaEti04npzu2jRAL39+4COqZf84bEO0SATggpeOZ+HQqz98wWxMm+yRqwwJhZoqWY1cnScPOwuwGTfBehxvF/rv4qrOdCETwIBXzDmV5bkFMYwjnLIDBmuM8WN5fidsGKqMGHWQEsov4vJiz+CepyEB0sbvSPzYoopuqlFMpdipd/yXY7RGCM/qZxI77BHKMewknNeIB15L2WmkB", "arraySize": 2587, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 133, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsFinanceQuerySubDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Subdomain"], "parameters": {"base64BitVector": "Lxd19ZHUQ8I2UWNjzirW9hg=", "arraySize": 133, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 253, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsSportsQueryDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "3PlVwEgFSNE73Dsk4ADpqH5POs6lvvqoZ4m8V8V9fQk=", "arraySize": 253, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 810, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsSportsQuerySubDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Subdomain"], "parameters": {"base64BitVector": "CBwFCO47SLRg+wxz5h95AQn6BkdLGidYIY/zsSIYdnypVB/G/gDcdJs+a/FjiBBHXjzqokMPoOB8/A11/PBAPQynkswFO7m92dewQsPP+tVEaDWXxIMxYIMWyEXrax/iBoRD+cQB", "arraySize": 810, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 23698, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsUniversityRankingQueryDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "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", "arraySize": 23698, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 12236, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsUniversityRankingQuerySubDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Subdomain"], "parameters": {"base64BitVector": "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", "arraySize": 12236, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 34894, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsCollegeEntranceExaminationSchoolQuery", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["EscapedKeywords"], "parameters": {"base64BitVector": "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", "arraySize": 34894, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 17531, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsCollegeEntranceExaminationMajorQuery", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["EscapedKeywords"], "parameters": {"base64BitVector": "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", "arraySize": 17531, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 309504, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsCollegeEntranceExaminationTopQuery", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["EscapedKeywords"], "parameters": {"base64BitVector": "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******************************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*************************************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", "arraySize": 309504, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 2333, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsTopNewsDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "oCJ+3EB28orUCRsmzjvlvNeRZjS1mKOaF1tQYh8XZkdhF/3gi6JEwIpZPOOZcKFvW/nnXxElhqpXoVV7cBbuY4SBxirYMOQUlq7m4Jr9InxRaAYL3jNGpBXKvdR/QWSxHHRxJNpLFXQ+id55uUEx+68Ta7MrU66fi0S43GrhvcQrro9LOQcZ+Gpfsvi5WcwGqFsWN8w3NbQtBlY+XkzBMtTLwIcfjTK3xMwEjoLig3IY8JZLamhSxAA7eSDECUVGqlFkuB/5CTNUQOpl6ec208K6Ho5QHpAWlD3uLXQdUk/Go/tyF6UlaSzhowtqXnWP/n8n5EaywXm9ytADq/yDaF0uyjQeRjbpVqunIQMQlbBI+Q0dlqm2xMym2wZglzKqoNMUEw==", "arraySize": 2333, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 181, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsResponsiveSiteDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "sHfgibN3wp6YWsl6MsbiFHWuCVBC1BQ=", "arraySize": 181, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 24, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsMsnDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "RUUF", "arraySize": 24, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"NumberOfHashFunctions": 8, "ShiftBaseValue": 3, "BloomFilterArraySize": 217, "PrimeBases": [5381, 5381, 5381, 5381], "signalName": "IsTravelSidebarDomain", "type": "<PERSON><PERSON><PERSON><PERSON>", "metadata": null, "inputs": ["Domain"], "parameters": {"base64BitVector": "eXjgfiVuYnW9kh3Irdxh++RlQjSMswnjjLN1AQ==", "arraySize": 217, "numHashes": 8, "shiftBase": 3, "primeBases": [5381, 5381, 5381, 5381]}}, {"signalName": "IsEduDomain", "type": "RegexMatchV2", "parameters": {"pattern": "LiouZWR1", "returnCaptured": false}, "inputs": ["Domain"], "metadata": null}, {"signalName": "IsRobloxQuery", "type": "RegexMatchV2", "parameters": {"pattern": "LipiaW5nLmNvbS8uKihyb2Jsb3guKj8pLio=", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsTravelQuery", "type": "RegexMatchV2", "parameters": {"pattern": "KC4qXC9cLy4qYWdvZGEuY29tXC9mbGlnaHRzXC9yZXN1bHRzLip8LipcL1wvLipjaGVhcG9haXIuY29tXC9haXJcL2xpc3RpbmcuKnwuKlwvXC8uKmV4cGVkaWEuY29tXC9mbGlnaHRzLXNlYXJjaC4qfC4qXC9cLy4qa2F5YWsuY29tXC9mbGlnaHRzLip8LipcL1wvLiptb21vbmRvLmNvbVwvZmxpZ2h0LXNlYXJjaC4qfC4qXC9cLy4qcHJpY2VsaW5lLmNvbVwvbVwvZmx5XC9zZWFyY2guKnwuKlwvXC8uKnNreXNjYW5uZXIuY29tXC90cmFuc3BvcnRcL2ZsaWdodHMuKnwuKlwvXC8uKnNvdXRod2VzdC5jb21cL2FpclwvYm9va2luZy4qfC4qXC9cLy4qdW5pdGVkLmNvbVwvZW5cL3VzXC9mc3JcL2Nob29zZS1mbGlnaHRzLiop", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "RegexMatchV2", "parameters": {"pattern": "KC4qXC9cLy4qbGlua2VkaW4uY29tXC9qb2JzXC92aWV3XC8uKnwuKlwvXC8uKmxpbmtlZGluLmNvbVwvam9ic1wvY29sbGVjdGlvbnNcLy4qY3VycmVudGpvYmlkPS4qfC4qXC9cLy4qc25hZ2Fqb2IuY29tXC9qb2JzLip8LipcL1wvLipjYXJlZXJidWlsZGVyLmNvbVwvam9iXC8uKnwuKlwvXC8uKm1vbnN0ZXIuY29tXC9qb2Itb3BlbmluZ3NcLyouKnwuKlwvXC8uKnppcHJlY3J1aXRlci5jb21cL2NcLy4rP1wvSm9iXC8uKik=", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "RegexMatchV2", "parameters": {"pattern": "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", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsKnowledgeCardQuery", "type": "RegexMatchV2", "parameters": {"pattern": "Xmh0dHBzPzpcL1wvKFteXC5dK1wuKSooaW1kYlwuY29tfG1zblwuY29tfHdpa2lwZWRpYVwub3JnKSh8XC8uKikk", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsSearchEngineResultPage", "type": "RegexMatch", "parameters": {"pattern": "^https:\\/\\/(www\\.)?google.(\\w*\\.?)*[^\\s?]*\\?[^\\s?]*([q]=[^\n]*)|^https:\\/\\/(\\w*\\.)*bing.com[^\\s?]*\\?[^\\s?]*([q]=[^\n]*)|^https:\\/\\/(\\w*\\.)*search\\.yahoo\\.com[^\\s?]*\\?[^\\s?]*([p]=[^\n]*)|^https:\\/\\/duckduckgo.com[^\\s?]*\\?[^\\s?]*([q]=[^\n]*)", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsNotifySearchEngineResultPage", "type": "RegexMatch", "parameters": {"pattern": "^https:\\/\\/(www\\.)?google.(\\w*\\.?)*[^\\s?]*\\?[^\\s?]*([q]=[^\n]*)", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsDiscoverAutoOpenRedirect", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL2VkZ2VzZXJ2aWNlcy5iaW5nLmNvbVwvZWRnZXN2Y1wvcmVkaXJlY3RcXD8oLiop", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsBingHomePage", "type": "RegexMatchV2", "parameters": {"pattern": "Xmh0dHBzOlwvXC93d3dcZCpcLihiaW5nfGJpbmctZXhwKVwuY29tKCR8XC8kfFwvXD98XC9ocFwvfFwvaHBcP3xcL2hwJClbXlwvXD9cbl0qJA==", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsUniversityRankingRegex", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL2VuXC53aWtpcGVkaWFcLm9yZ1wvd2lraVwvW15cL10qKFVuaXZlcnNpfEluc3RpdHV8Q29sbGVnZXxBY2FkZW15fFNjaG9vbHxJSVR8VGVjaHxFZHUpW15cL10q", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "RegexMatchV2", "parameters": {"pattern": "Xi4qKGNvdmlkfGxvY2tkb3dufG9taWNyb258Y29yb25hfHZhY2NpbmV8dmFyaWFudHxkZWx0YXxlcHNpbG9ufGdhbW1hfGJldGF8YWxwaGF8a2FwcGF8ZXBpZGVtaWN8cGFuZGVtaWMpLiok", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsBingPersistentSearchQuery", "type": "RegexMatch", "parameters": {"pattern": "^https:\\/\\/www.bing.com\\/ck\\/a\\?[^\\\\s?].*(psq=[^&\\n]+)(.*)", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsBaiduSearchQuery", "type": "RegexMatch", "parameters": {"pattern": "https:\\/\\/www\\.baidu\\.com\\/s.*[&]wd=.+", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsBingChatLink", "type": "RegexMatchV2", "parameters": {"pattern": "Xmh0dHBzOlwvXC93d3cuYmluZy5jb21cL3NlYXJjaFw/W15cXHM/XS4qKHNob3djb252PTEpKC4qKQ==", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsUndersidePersistentChatLink", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL2VkZ2VzZXJ2aWNlcy5iaW5nLmNvbVwvZWRnZXN2Y1wvcmVkaXJlY3RcP3VybD0oLispKGtleT1wc2MtdW5kZXJzaWRlKSguKik=", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsUndersideMarketingLink", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL3d3dy5taWNyb3NvZnQuY29tKFwvLiopP1wvKGVkZ2V8YmluZykoXC8uKik/XD8oLipcJik/cHJvbXB0KFs9Jl0oW14mXSspLiopPyQ=", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsTwitchNonStreamPage", "type": "RegexMatchV2", "parameters": {"pattern": "Xig/Omh0dHBzPzpcL1wvKT8oPzp3d3dcLik/KD86dHdpdGNoXC50dlwvKSg/OmRpcmVjdG9yeXxzZWFyY2h8ZW1haWwtdW5zdWJzY3JpYmV8bG9naW58c2lnbnVwKSg/OihcP3xcLylcUyopKiQ=", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsTwitchSubPage", "type": "RegexMatchV2", "parameters": {"pattern": "KD86aHR0cHM/OlwvXC8pPyg/Ond3d1wuKT8oPzp0d2l0Y2hcLnR2XC8pKChcdykpKD86XFMrKT8=", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsTextPage", "type": "RegexMatchV2", "parameters": {"pattern": "KD86aHR0cHM/OlwvXC8pPyg/Ond3d1wuKT8oPzptc25cLmNvbVwvLitcL25ld3NcLy4qKQ==", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsVideoPage", "type": "RegexMatchV2", "parameters": {"pattern": "KD86aHR0cHM/OlwvXC8pPyg/Ond3d1wuKT8oPzp5b3V0dWJlXC5jb21cL3dhdGNoXD8uKik=", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsPdfPage", "type": "RegexMatchV2", "parameters": {"pattern": "KC4rXC5wZGYkKQ==", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsInterceptMarketingPage", "type": "RegexMatchV2", "parameters": {"pattern": "Xmh0dHBzOlwvXC9nby5taWNyb3NvZnQuY29tXC9md2xpbmtcL1w/bGlua2lkPTIyMTI1Mzgk", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsChromeDownloadThankyouPage", "type": "RegexMatchV2", "parameters": {"pattern": "Xmh0dHBzOlwvXC93d3cuZ29vZ2xlLmNvbVwvKC4qXC8pP2Nocm9tZVwvKG5leHQtc3RlcHN8dGhhbmsteW91KS5odG1sLiok", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsLipokerGamePage", "type": "RegexMatchV2", "parameters": {"pattern": "Xmh0dHBzOlwvXC9saXBva2VyXC5pb1wvZ2FtZVwvWzEtOV1bMC05XSo=", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsBingSearchResultPage", "type": "RegexMatchV2", "parameters": {"pattern": "Xmh0dHBzOlwvXC8oXHcqXC4pKmJpbmcuY29tW15ccz9dKlw/W15ccz9dKihbcV09W14KXSop", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsGamesShorelineAutoShow", "type": "RegexMatchV2", "parameters": {"pattern": "KGh0dHBzOlwvXC8pKih3d3dcLmNoZXNzXC5jb218aHRtbDVcLmdhbWVkaXN0cmlidXRpb25cLmNvbVwvW2EtejAtOV9cLlwtXCtdK3x3d3dcLmdzblwuY29tXC9bYS16MC05X1wuXC1cK10rfHNob2Nrd2F2ZVwuY29tXC9nYW1lbGFuZGluZ1wvW2EtejAtOV9cLlwtXCtdK1wuanNwfG1haGpvbmdcLmNvbVwvZ2FtZVwvW2EtejAtOV9cLlwtXCtdK3xzb2xpdHJcLmNvbXx3b3JsZG9mc29saXRhaXJlXC5jb218c29saXRhaXJlZFwuY29tfGZyZWVvbmxpbmVnYW1lc1wuY29tXC9nYW1lXC9bYS16MC05X1wuXC1cK10rfHd3d1wuMjQ3W2EtejAtOV9cLlwtXCtdK1wuY29tfGExMFwuY29tXC9nYW1lXC9bYS16MC05X1wuXC1cK10rfHk4XC5jb21cL2dhbWVzXC9bYS16MC05X1wuXC1cK10rfG5ld2dyb3VuZHNcLmNvbVwvcG9ydGFsXC92aWV3XC9bYS16MC05X1wuXC1cK10rfGFnYW1lXC5jb21cL2dhbWVzXC9bYS16MC05X1wuXC1cK10rfGFybW9yZ2FtZXNcLmNvbVwvW2EtejAtOV9cLlwtXCtdK1wvW2EtejAtOV9cLlwtXCtdK3xraXppXC5jb21cL2dhbWVzXC9bYS16MC05X1wuXC1cK10rfHBva2lcLmNvbVwvXHcrXC9nXC9bYS16MC05X1wuXC1cK10rfGFkZGljdGluZ2dhbWVzXC5jb21cL1thLXowLTlfXC5cLVwrXStcL1thLXowLTlfXC5cLVwrXSt8YmlnZmlzaGdhbWVzXC5jb21cL1x3K1wvXHcrXC9nYW1lXC9bYS16MC05X1wuXC1cK10rXC5odG1sfGFya2FkaXVtXC5jb21cL2dhbWVzXC9bYS16MC05X1wuXC1cK10rfG5hbm9cLnViaXNvZnRcLmNvbVwvXD9nYW1laWRcPVthLXowLTlfXC5cLVwrXSt8bmFub1wudWJpc29mdFwuY29tfHpvbmVcLm1zblwuY29tXC9nYW1lcGxheWVyXC9nYW1lcGxheWVyaHRtbFwuYXNweFw/Z2FtZVw9W2EtejAtOV9cLlwtXCtdK3xhcHBzXC5mYWNlYm9va1wuY29tXC9bYS16MC05X1wuXC1cK10rfGNhcmRnYW1lc1wuaW9cL1thLXowLTlfXC5cLVwrXSt8d3d3XC5mYWNlYm9va1wuY29tXC9nYW1pbmdcL3BsYXlcL1thLXowLTlfXC5cLVwrXSt8d3d3XC5wb2dvXC5jb21cL2dhbWVzXC9bYS16MC05X1wuXC1cK10rfGdhbWVzXC5hYXJwXC5vcmdcL2dhbWVzXC9bYS16MC05X1wuXC1cK10rfHd3d1wubnl0aW1lc1wuY29tXC9jcm9zc3dvcmRzfHd3d1wubnl0aW1lc1wuY29tXC9wdXp6bGVzXC9bYS16MC05X1wuXC1cK10rfGdhbWVzXC4odXNhdG9kYXl8d2FzaGluZ3RvbnBvc3QpXC5jb21cL2dhbWVzXC9bYS16MC05X1wuXC1cK10rfHd3d1wubWluaWNsaXBcLmNvbVwvZ2FtZXNcL1thLXowLTlfXC5cLVwrXSt8d3d3XC5rb25ncmVnYXRlXC5jb21cL2dhbWVzXC9bYS16MC05X1wuXC1cK10rfHd3d1wubnl0aW1lc1wuY29tXC9nYW1lc1wvW2EtejAtOV9cLlwtXCtdKykoXC8pKg==", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsMsnArticleUrlFromNtpP1P2", "type": "RegexMatchV2", "parameters": {"pattern": "CiAgICAgICAgKC4qWy5dKSptc24uY29tLiovYXItLipvY2lkPWVudG5ld3NkaHAuKnwoLipbLl0pKm1zbi5jb20uKi9hci0uKm9jaWQ9ZW50bmV3c250cC4qfCguKlsuXSkqbXNuLmNvbS4qL2FyLS4qb2NpZD1tc2VkZ2RocC4qfCguKlsuXSkqbXNuLmNvbS4qL2FyLS4qb2NpZD1tc2VkZ250cC4qfCguKlsuXSkqbXNuLmNvbS4qL2FyLS4qb2NpZD1tc2VkZ2RocGhkci4qfCguKlsuXSkqbXNuLmNvbS4qL2FyLS4qb2NpZD1jaHJvbWVudHBuZXdzLip8KC4qWy5dKSptc24uY29tLiovYXItLipvY2lkPW1zZWRnbnRwaGRyLip8KC4qWy5dKSptc24uY29tLiovYXItLipvY2lkPXdpbnAxdGFza2Jhci4qfCguKlsuXSkqbXNuLmNvbS4qL2FyLS4qb2NpZD13aW5wMi4q", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsMsnArticleAutoOpenFromP1P2", "type": "RegexMatchV2", "parameters": {"pattern": "KC4qWy5dKSptc24uY29tLiovYXItLipvY2lkPXdpbnAxdGFza2Jhci4qfCguKlsuXSkqbXNuLmNvbS4qL2FyLS4qb2NpZD13aW5wMi4q", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsExtensionPathFinderSearchQuery", "type": "RegexMatchV2", "parameters": {"pattern": "LiooYmFpZHVcLmNvbS4qP1tcP3wmXXdkfHNvXC5jb20uKj9bXD98Jl1xfHNvZ291XC5jb20uKj9bXD98Jl1xdWVyeSk9KFteJl0rKS4q", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsAutoOpenSidebarSearchQuery", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL3d3d1wuYmFpZHVcLmNvbVwvLipbXD98Jl1wbj0uKnxodHRwczpcL1wvd3d3XC5zb2dvdVwuY29tXC8uKltcP3wmXXBhZ2U9Lip8aHR0cHM6XC9cL3d3d1wuc29cLmNvbVwvLipbXD98Jl1wbj0uKg==", "returnCaptured": false}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsYahooJapanTopSites", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL3d3d1wueWFob29cLmNvXC5qcC4q", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsYahooJapanWeather", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL3dlYXRoZXJcLnlhaG9vXC5jb1wuanBcL3dlYXRoZXJcLyhqcFwvKFsxLTldfFsxLTNdWzAtOV18NFswLTddfDFbYS1kXSlcLyhcZHs0fSk/KFwvKFxkezQsNX0pKT8oXD9kYXk9WzEtOF18XC5odG1sKSk/JA==", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsYahooJapanWeatherTop", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL3dlYXRoZXJcLnlhaG9vXC5jb1wuanBcL3dlYXRoZXJcLyQ=", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsYahooJapanFinance", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL2ZpbmFuY2VcLnlhaG9vXC5jb1wuanAuKg==", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsYahooJapanNews", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL25ld3NcLnlhaG9vXC5jb1wuanAuKg==", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsYahooTaiwanTopSite", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL3R3XC55YWhvb1wuY29tLio=", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsYahooTaiwanNews", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL3R3XC5uZXdzXC55YWhvb1wuY29tLio=", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsNaverTopSite", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL3d3d1wubmF2ZXJcLmNvbS4q", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsNaverNews", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL25cLm5ld3NcLm5hdmVyXC5jb20uKg==", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsWaitlistLink", "type": "RegexMatchV2", "parameters": {"pattern": "LipuZD03ajYzZ2szYmdmdGp3czh3Lio=", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsEchoLoginError", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL2FjY291bnRzXC5nb29nbGVcLmNvbVwvKFNlcnZpY2VMb2dpbnxpbmZvXC91bmtub3duZXJyb3J8cmVzdGFydHxiXC8wXC9BZGRNYWlsU2VydmljZXxBZGRTZXNzaW9uKS4q", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsEchoUnknownError", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL2FjY291bnRzXC5nb29nbGVcLmNvbVwvdjNcL3NpZ25pblwvdW5rbm93bmVycm9yLio=", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsEchoJSDisabledError", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL2FjY291bnRzXC5nb29nbGVcLmNvbVwvdjNcL3NpZ25pblwvcmVqZWN0ZWQuKg==", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsEchoAuthExpiredError", "type": "RegexMatchV2", "parameters": {"pattern": "aHR0cHM6XC9cL2FjY291bnRzXC5nb29nbGVcLmNvbVwvaW5mb1wvc2Vzc2lvbmV4cGlyZWQuKg==", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsGamerModeTopSite", "type": "RegexMatchV2", "parameters": {"pattern": "KD86aHR0cHM/OlwvXC8pPyg/Ond3d1wuKT8oPzpcdytcLik/KG1pbmVjcmFmdFwubmV0fHhib3hcLmNvbSkuKg==", "returnCaptured": true}, "inputs": ["Url"], "metadata": null}, {"signalName": "IsCompeteAISite", "type": "RegexMatchV2", "parameters": {"pattern": "Lioob3BlbmFpXC5jb218Y2hhdGdwdFwuY29tfGFpXC5nb29nbGV8YW50aHJvcGljXC5jb218bWlkam91cm5leVwuY29tfHBlcnBsZXhpdHlcLmFpfHF1aWxsYm90XC5jb218ZGVlcGxcLmNvbXxkZWVwc2Vla1wuY29tKS4q", "returnCaptured": true}, "inputs": ["Domain"], "metadata": null}, {"signalName": "IsCompeteAIChatSite", "type": "RegexMatchV2", "parameters": {"pattern": "Lioob3BlbmFpXC5jb218Y2hhdGdwdFwuY29tfGNsYXVkZVwuYWl8YWlcLmdvb2dsZXxwZXJwbGV4aXR5XC5haXxkZWVwc2Vla1wuY29tKS4q", "returnCaptured": true}, "inputs": ["Domain"], "metadata": null}, {"signalName": "IsCompeteAIWriteSite", "type": "RegexMatchV2", "parameters": {"pattern": "LioocXVpbGxib3RcLmNvbXxjbGF1ZGUuYWkpLio=", "returnCaptured": true}, "inputs": ["Domain"], "metadata": null}, {"signalName": "IsCompeteAIImageSite", "type": "RegexMatchV2", "parameters": {"pattern": "LioobWlkam91cm5leVwuY29tKS4q", "returnCaptured": true}, "inputs": ["Domain"], "metadata": null}, {"signalName": "IsCompeteAITranslateSite", "type": "RegexMatchV2", "parameters": {"pattern": "LiooZGVlcGxcLmNvbSkuKg==", "returnCaptured": true}, "inputs": ["Domain"], "metadata": null}, {"signalName": "IsSplitWindowPromotion", "type": "SubstringSetMatch", "parameters": {"keywordList": ["bard.google.com"], "wholeWordMatch": true, "matchStrategy": "Any", "returnMatched": false}, "inputs": ["Subdomain"], "metadata": null}]}