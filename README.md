# Microsoft Rewards Automation Bot (Node.js/Playwright)

This project is a Node.js implementation of an automation bot for Microsoft Rewards, migrated from a Python/Selenium codebase. It uses <PERSON><PERSON> to automate tasks such as daily searches, completing daily sets, and other activities to earn Microsoft Rewards points.

## Features

-   **Automated Daily Searches**: Performs desktop and mobile searches.
-   **Daily Set Completion**: Navigates and completes daily set activities.
-   **Other Activities**: Attempts to complete other available rewards activities.
-   **Points Balance Check**: Logs initial and final points balance.
-   **Persistent Browser Session**: Uses a persistent `userDataDir` to maintain login sessions.
-   **Logging**: Basic logging to console and file.

## Prerequisites

Before you begin, ensure you have the following installed:

-   Node.js (LTS version recommended)
-   npm (Node Package Manager) or Yarn

## Installation

1.  **Clone the repository (if you haven't already):**

    ```bash
    git clone <repository_url>
    cd ms-rewards-automation
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    # or if you use yarn
    yarn install
    ```

    This will install <PERSON>wright and its browser binaries (Chromium, Firefox, WebKit).

## Configuration

Configuration is done directly within the `index.js` file for now. You can adjust the following parameters in the `MicrosoftRewardsBot` constructor:

-   `desktopSearchCount`: Number of desktop searches to perform (default: 30).
-   `mobileSearchCount`: Number of mobile searches to perform (default: 20).
-   `searchTerms`: An array of strings to use as search queries. The bot will randomly pick from this list.
-   `headless`: Set to `true` to run the browser in headless mode (no UI), `false` for headed mode (browser UI visible). Default is `false`.
-   `browserType`: The browser to use. Options: `'chromium'`, `'firefox'`, `'webkit'`. Default is `'chromium'` (Edge).

Example configuration in `index.js`:

```javascript
const bot = new MicrosoftRewardsBot({
    desktopSearchCount: 30,
    mobileSearchCount: 20,
    searchTerms: [
        'weather today',
        'news headlines',
        'latest tech gadgets',
        // ... add more search terms
    ],
    headless: false, // Set to true for headless operation
    browserType: 'chromium' // Use 'chromium' for Edge, 'firefox', or 'webkit'
});
```

## Usage

To run the bot, execute the `index.js` file using Node.js:

```bash
node index.js
```

### First Run / Login

On the first run, or if your session expires, the bot will navigate to the Microsoft login page. You will need to manually log in through the opened browser window. Once logged in, the bot will attempt to continue with the automation tasks. The session will be saved in the `user_data` directory, so subsequent runs should not require re-login unless the session expires or is invalidated.

### Logging

Logs are output to the console and also saved to `ms_rewards_automation.log` in the project root directory. This file can be useful for debugging or reviewing bot activity.

## Troubleshooting

-   **Bot not performing actions**: Ensure the browser window is not minimized or obscured if running in headed mode. Sometimes, elements might not be interactable if the window is not in focus.
-   **Login issues**: If you frequently face login issues, try deleting the `user_data` directory to force a fresh login.
-   **Element not found errors**: Microsoft Rewards website structure might change. If the bot stops working, it's likely due to changes in element selectors (XPaths). These would need to be updated in the `index.js` file.
-   **Playwright browser issues**: Ensure all Playwright dependencies are correctly installed. You can try reinstalling them with `npx playwright install`.

## Disclaimer

This bot is for educational purposes and personal use only. Use it responsibly and be aware of Microsoft's terms of service regarding automated activity. The author is not responsible for any consequences resulting from the misuse of this software.

## License

This project is open-source and available under the MIT License.