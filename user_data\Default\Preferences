{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_info": [{"access_point": 17, "account_id": "000600001C1980E4", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 3, "edge_account_cid": "5a87b2492eaa56e6", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "<PERSON>", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "<PERSON>", "edge_account_location": "ID", "edge_account_oid": "", "edge_account_phone_number": "************", "edge_account_puid": "000600001C1980E4", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "000600001C1980E4", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "", "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_last_notification_shown": "*****************", "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"available_dark_theme_options": "All", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "copilot_vision_settings_button": true, "edge_sidebar_visibility": {"_game_assist_": {"order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "add_app_to_bottom": true, "order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "edge_sidebar_visibility_debug": {"order_list": ["Search"], "order_raw_data": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "Search", "pos": "**********"}}}, "enable_text_prediction_v2": true, "gamer_mode_asset_store_prefs": {"779d97ed-2254-4943-a1f3-c811fa709092": {"gamer_mode_modal_script_hash": "xie40asvhdbPXzggtqUJ4lfglpLAYbJeXpWhq51+U+s=", "gamer_mode_modal_script_url": "https://edgeassetservice.azureedge.net/assets/gamer_mode_modal_ux/1.1.69/asset?assetgroup=GamerModeModalUX"}}, "has_seen_welcome_page": false, "hub_app_non_synced_preferences": {"apps": {"06be1ebe-f23a-4bea-ae45-3120ad86cfea": {"last_path": ""}, "0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "25fe2d1d-e934-482a-a62f-ea1705db905d": {"last_path": ""}, "35a43603-bb38-4b53-ba20-932cb9117794": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "a1a78183-6db3-4789-9e7c-84d157846d55": {"last_path": ""}, "bacc3c12-ebff-44b4-a0f8-ce8b69c9e047": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {}, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "cleanup_start_v2"}], "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_hub_app_in_sidebar_buttons": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}, "show_hub_app_in_sidebar_buttons_legacy": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}, "show_hub_app_in_sidebar_buttons_legacy_update_time": "13396954598089439", "time_of_last_normal_window_close": "13396954995817354", "underside_chat_bing_signed_in_status": true, "user_level_features_context": {}, "window_placement": {"bottom": 956, "left": 10, "maximized": false, "right": 526, "top": 10, "work_area_bottom": 1040, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "browser_content_container_height": 857, "browser_content_container_width": 500, "browser_content_container_x": 0, "browser_content_container_y": 81, "cached_fonts": {"search_results_page": {"fonts": ["Roboto", "<PERSON><PERSON>"]}}, "collections": {"prism_collections": {"enabled": 0, "policy": {"cached": 0}}}, "commerce_daily_metrics_last_update_time": "13396951348755968", "continuous_migration": {"equal_opt_out_users_data": {"backfilled": true}}, "copilot_vision": {"onboard_shown": true, "user_access": true}, "countryid_at_install": 18756, "custom_links": {"list": []}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_mode": {"enabled_state": 1}, "consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "https://go.microsoft.com/fwlink/?linkid=2133855&bucket=13", "consumer_sitelist_version": "97", "external_consumer_shared_cookie_data": {}, "profile_id": "7DZ97TIZ", "sitelist_has_consumer_data": true}, "edge": {"account_type": 1, "bookmarks": {"last_dup_info_record_time": "*****************"}, "msa_sso_info": {"allow_for_non_msa_profile": false}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "profile_sso_option": 1, "services": {"account_id": "000600001C1980E4", "last_gaia_id": "000600001C1980E4", "last_username": "<EMAIL>", "signin_scoped_device_id": "7d8a81dc-faeb-483d-b63d-b06fd212ae3d"}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"fromCache\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":9,\"workspacesForExternalLinks\":[]}", "storage": {"state": "{\"app_folder_path\":\"\",\"container_id\":\"\",\"drive_id\":\"\",\"prefs_item_id\":\"\",\"storage_endpoint\":\"\",\"version\":3}"}}}, "edge_fundamentals_appdefaults": {"ess_lightweight_version": 101}, "edge_rewards": {"cache_data": "CAEQqgMYAEIMMDAwODg2MDAwMDA1SgJpZA==", "hva_promotions": [], "promotions": [{"attributes": {"State": "<PERSON><PERSON><PERSON>", "activityprogress": "0", "animated_icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_small.png", "complete": "False", "description": "Search here for 3 days and earn an extra 3,100 points.", "destination": "", "edgebar_description": "", "edgebar_disclaimer": "Offer valid for 1 person/account within 7 days of joining the challenge", "edgebar_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Microsoft_giftcard_grey.png", "edgebar_link_text": "Get started", "edgebar_title": "Welcome to search bar powered by Microsoft Edge! Get a free gift card when you search here for 3 days.", "edgebar_type": "eligible", "give_eligible": "False", "icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/278x180/Star-magenta-278x180px.png", "link_text": "Get started", "max": "0", "offerid": "eligibility_EdgeBarMicrosoft_202211_ML293H", "progress": "0", "promotional": "0", "sc_bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_medium.png", "sc_bg_large_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_large.png", "small_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Mobile/newEdgeLogo_75x75.png", "title": "Earn bonus Microsoft Rewards points", "type": "url<PERSON><PERSON>"}, "name": "ENID_eligibility_EdgeBarMicrosoft_202211_ML293H_info", "priority": -1, "tags": ["exclude_give_pcparent", "non_global_config"]}], "refresh_status_muted_until": "13397556148642925"}, "edge_ux_config": {"assignmentcontext": "xJNfcAKWw8TAHPb3msJUAWprdMpyjJbl+SaSKDJaZQ0=", "dataversion": "254491176", "experimentvariables": {"2f717976": {"edgeServerUX.sync.historyDataTypeEnabled": true}, "shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}, "shoprevenuattributiont": {"edgeServerUX.shopping.disableCashbackOnCouponCopy": true}}, "flights": {"2f717976": "31213786", "shopppdismisstreatment": "31004791", "shoprevenuattributiont": "31235887"}, "latestcorrelationid": "Ref A: 154E6550B03C4B10A7BB6218FE0D9B45 Ref B: SG1EDGE0312 Ref C: 2025-07-14T08:14:39Z"}, "edge_wallet": {"passwords": {"password_lost_report_date": "13396951378653024"}, "trigger_funnel": {"records": []}}, "enterprise_profile_guid": "6196f581-7fa3-494f-a456-4cb8bf645048", "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extension": {"installed_extension_count": 4}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.3351.83", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": [], "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover extensions for Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.83\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "bhmhibnbialendcafinliemndanacfaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "bobbggphonhgdonfdibkfipfepfcildj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ceaifoolopnigfpidlheoagpheiplgii": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.83\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "eijpepilkjkofamihbmjcnihgpbebafj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "enkoeamdnimieoooocohgbdajhhkajko": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ghglcnachgghkhbafjogogiggghcpjig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "hloomjjkinpbjldhobfkfdamkmikjmdo": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "hmlhageoffiiefnmojcgoagebofoifpl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "iglcjdemknebjbklcgkfaebgojjphkec": {"account_extension_type": 0, "active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "w", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "Discover extensions for Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate", "identity", "hubPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.83\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "khffkadolmfbdgahbabbhipadklfmhgf": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kjncpkplfnolibapodobnnjfgmjmiaba": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kmojgmpmopiiagdfbilgognmlegkonbk": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.83\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "olmhchkiafniffcaiciiomfdplnmklak": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "pencekojiebcjhifbkfdncgmmooepclc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ppnnjfpaneghjbcepgedmlcgmfgkjhah": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}}}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 138}, "google": {"services": {"consented_to_sync": true, "last_signed_in_username": "<EMAIL>"}}, "https_upgrade_navigations": {"2025-07-14": 90}, "intl": {"selected_languages": "en-US,en"}, "language_dwell_time_average": {"en": 12.714285714285714}, "language_model_counters": {"en": 48}, "language_usage_count": {"en": 14}, "local_browser_data_share": {"index_last_cleaned_time": "13396951408763784", "pin_recommendations_eligible": false}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "p/iX6xPZBYEf8diKvXNStXVU8LNOpuG5l1ub9xGrPyDNEq19CIvX9A4dTTZ1jNPbcW4JqVfekP6IPYnhpzJrjw=="}, "muid": {"last_sync": "13396951348755914", "values_seen": ["26D34993E00D6E3016FA5FBAE1576F64"]}, "ntp": {"next_site_suggestions_available": false, "num_personal_suggestions": 3}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": true, "personalization_in_context_count": 0}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {"https://rewards.bing.com:443,*": {"last_modified": "13396954990132260", "setting": {"client_hints": [8, 9, 10, 11, 12, 13, 14, 16, 23]}}, "https://rewards.microsoft.com:443,*": {"last_modified": "13396954989855734", "setting": {"client_hints": [9, 10, 11, 12, 13, 14, 16, 23]}}, "https://www.bing.com:443,*": {"last_modified": "13396954949593897", "setting": {"client_hints": [6, 8, 9, 10, 11, 12, 13, 14, 16, 23]}}, "https://www.microsoft.com:443,*": {"last_modified": "13396952049549199", "setting": {"client_hints": [9, 11, 13, 14, 15, 16]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]bing.com,*": {"last_modified": "13396954990140469", "setting": {}}, "https://[*.]live.com,*": {"last_modified": "13396951354250328", "setting": {}}, "https://[*.]microsoft.com,*": {"last_modified": "13396952050953479", "setting": {}}, "https://[*.]microsoftonline.com,*": {"last_modified": "13396951353837555", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://login.live.com:443,*": {"expiration": "13404727355825452", "last_modified": "13396951355825454", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://login.microsoftonline.com:443,*": {"expiration": "13404727354250788", "last_modified": "13396951354250791", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://rewards.bing.com:443,*": {"expiration": "13404730995813213", "last_modified": "13396954995813218", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 5}}, "https://www.bing.com:443,*": {"expiration": "13404730957384544", "last_modified": "13396954957384546", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.microsoft.com:443,*": {"expiration": "13404728057919760", "last_modified": "13396952057919762", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://login.microsoftonline.com:443,*": {"last_modified": "13396951353838462", "setting": {"lastEngagementTime": 1.3396951353838452e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "https://rewards.bing.com:443,*": {"last_modified": "13396954990141467", "setting": {"lastEngagementTime": 1.3396954990141448e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "https://www.bing.com:443,*": {"last_modified": "13396954949268929", "setting": {"lastEngagementTime": 1.3396954949268924e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "https://www.microsoft.com:443,*": {"last_modified": "13396952050954727", "setting": {"lastEngagementTime": 1.339695205095472e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.3351.83", "creation_time": "13396951348621155", "edge_crash_exit_count": 0, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "aba2a0be-fab2-4991-b05e-45cedc8e9851", "edge_user_with_non_zero_passwords": false, "exit_type": "Normal", "has_seen_signin_fre": false, "is_relative_to_aad": true, "last_engagement_time": "13396954990141449", "last_time_obsolete_http_credentials_removed": 1752477808.651451, "last_time_password_store_metrics_reported": **********.65177, "managed_user_id": "", "name": "Profile 1", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_138.0.3351.83": 207.0}, "password_hash_data_list": [], "signin_fre_seen_time": "*****************", "were_old_google_logins_removed": true}, "profiles": {"edge": {"web_signin_cta_shown_records": []}}, "protection": {"macs": {"browser": {"show_home_button": "278AEB88CA8FEF995464099C96CF12ADBD81AB9B4C0D484BE4E20F4AEC9B9EAB"}, "default_search_provider_data": {"template_url_data": "74874177EC8ED6DB082F45669073DFDE18E3702A6E3E0EFC249D710915D27E3E"}, "edge": {"services": {"account_id": "8D0CC0689054CF78BC84ADF871BF030DBAC74E8F5D99FC499BEB1DCFED0820E4", "last_username": "A3568010D4FAECA00020A05A264E038F7935C3E65078D65AC31F6F174B561939"}}, "enterprise_signin": {"policy_recovery_token": "182E2D4350D44CC524E5C3729AEB8E18BC123CFFC1B67D26C116585D62566063"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "390A49EB50AC5B7D72E8A54860101E93CF0E19D9F4C75D2D053C0DC5D64D04E5", "bhmhibnbialendcafinliemndanacfaj": "D168A343480BD67683D3FF88C6F88A786B8669A37BEB5232AAB95C5FE1D8A6D6", "bobbggphonhgdonfdibkfipfepfcildj": "2A223B8C25FD8271DCA7F8E4FEF6B73DB29D75836E3E8A4A22B34BBF58B8295D", "ceaifoolopnigfpidlheoagpheiplgii": "D1198DC9D6A9EDD7F8FEC7A220318B45462610F669A2FBCC22A369CDB37AABEC", "cjneempfhkonkkbcmnfdibgobmhbagaj": "3E5421ACF7671C869E7F239FA79172E2FB4EA2F528EB74A7DFD09C4D9CDB7513", "dcaajljecejllikfgbhjdgeognacjkkp": "231F91FAE9A90F475632040BE1BB9CC572CC524512B0C02CA7F5480C1848979D", "dgiklkfkllikcanfonkcabmbdfmgleag": "EDFF5675EB45A63A8AE2BDFD9A34460B0B6A50F456A330AF21ABCD3AA5701A7F", "ehlmnljdoejdahfjdfobmpfancoibmig": "E64A94597C67708E4B3C1C1B0C531A4632AF24CADA05ED5C803572AB602D4C3E", "eijpepilkjkofamihbmjcnihgpbebafj": "CB7CDFC76EA1BC3BD641F9DADC6F71B8A7640C821C745ECF94B6AEA77F1CAE7C", "enkoeamdnimieoooocohgbdajhhkajko": "C7EB7068D80C53029711FB79E68B2DAD0F12E65F812B7E84E453176A45C23007", "fjngpfnaikknjdhkckmncgicobbkcnle": "88356B3F90A5817D947435AC7EA84C908C1660C3EBFDA0D7D864E1F3AC6707E2", "gbihlnbpmfkodghomcinpblknjhneknc": "E748D42E972039573C853ECE90F4EED672139A6708AC2A2A9C1FE81A6D149D92", "gbmoeijgfngecijpcnbooedokgafmmji": "A974EE0AF473526AFA6B4670DD1A41EC4D5A4D5DD218B9CA4CE128B9935CDE82", "gecfnmoodchdkebjjffmdcmeghkflpib": "60C23B41562AE80DFFEC0BA3BCAE87D61D3678051456C6B4BDC59C59D6291257", "ghglcnachgghkhbafjogogiggghcpjig": "BDEB6E2F74648CB208A6982A6B951C057DB05EC91D9E6CB9A7C21645B8EB54C6", "hfmgbegjielnmfghmoohgmplnpeehike": "0E6D459D8286222CDBCAF2AE1518266CBA597D0B5E82978EDCB244A245689C43", "hloomjjkinpbjldhobfkfdamkmikjmdo": "81276FCC5C12480252D0FB60A2053D16545161FB1CC073BAE31388D669361675", "hmlhageoffiiefnmojcgoagebofoifpl": "45473A9E1D70EF52379EB0495F8D4282E7C09DC3174828BEA067201D0FC1ABBA", "iglcjdemknebjbklcgkfaebgojjphkec": "501E6DAAAFC5DE230561CAE4E4DC465208B2409D92AB01EE11474B6DA7109F0E", "jbleckejnaboogigodiafflhkajdmpcl": "5848EA19017A0DE480AEBF2E255E52EA5A3F1D089AE407F24F931687FAD58D52", "kfihiegbjaloebkmglnjnljoljgkkchm": "EAC949F2631070BEF057E7D02B016D41F4E23655ACC729242BF034B46C3FA8A8", "khffkadolmfbdgahbabbhipadklfmhgf": "2883A3BDCD55E4A234C092D4B33C120AEFB08231AFA035453478B6D5C62A5109", "kjncpkplfnolibapodobnnjfgmjmiaba": "E21AA8FEF23BE4C27E583933AC747D5D83E8329BDE23F41706FD5EC4DE06DB26", "kmojgmpmopiiagdfbilgognmlegkonbk": "9BF49E9B46B23AEA74ACEBD6081C75A1C352ED787E6B0AC952F2946E6B83A114", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "3E48FB1D3F47722A13F9FE2728EA2C5CC53FE6603CEA6673002F7FC5AE57920B", "nkbndigcebkoaejohleckhekfmcecfja": "1898A33862FD83F4C278F5281B3A1A8572DDDB5E5EA3259D1DA3EB378DCADE70", "ofefcgjbeghpigppfmkologfjadafddi": "8A21507994F4D55E189162DAD345A61A3C1D1B921C6417FDE90A5AA841B4BC0C", "olmhchkiafniffcaiciiomfdplnmklak": "2570C5255231C00A09E0AEA67F131A4C3646DF927142DA95F8A762B9770A6C94", "pencekojiebcjhifbkfdncgmmooepclc": "0F9225EFFEAE70637C4CFB5967C7556F62EF4E9B19B2F47F65307FE27751CEE5", "ppnnjfpaneghjbcepgedmlcgmfgkjhah": "2B9C9A85D1454BA94AEB9E6554A5FCC4A0F7557248586D6EB56AA8DD809B43E7"}, "ui": {"developer_mode": "21AF8266EE9B1544CD6D7BF065DC3B08D84B94E4F6CF849B37363DD866D30D8B"}}, "google": {"services": {"last_signed_in_username": "B841B7128E197FBE05F259EEC5D0BBBC9DDDE35C630AA6C57A5BA31F406305C8"}}, "homepage": "4468690148DD7B3A2D3078E8D1571BB3963AF04564AC8F22F82F1AA8FB136FB0", "homepage_is_newtabpage": "456CA59C7395464F42B324C19BA252D3A7A4C56ACAAEA2D3FB4BFB7FC55F52B9", "media": {"cdm": {"origin_data": "457DEB14E28C9873409C353AFA312667D5E80C9B453FA9729DA067B1C75BE77B"}, "storage_id_salt": "89DD47EFA8F01E151D71A5C2141E18C9269A8151E697AA71ACC18E02C5316727"}, "pinned_tabs": "4F9E0A3EA6FBE440A248760441284196382F10E45D3EF126093C874715B24A44", "prefs": {"preference_reset_time": "70BE4612160528708B5833C8426A562355ECAB80704F397F1F9C1952F8EE6A2E"}, "safebrowsing": {"incidents_sent": "2F572CCCC1CB672532FD925B6EB8B0F6E431BBEF4898C772E2ECAC4AEE75EF96"}, "search_provider_overrides": "C875758BE7952D956092093089172166736FEC67D3258C0625EA281B9CFC39D3", "session": {"restore_on_startup": "032CE9B75EA706FF92213FD52A684EDE2BFC2232C87B66E7A63A7D8C5EA546EC", "startup_urls": "64C1614627A9FD075B748C3C99E9F826ACA768550B142D433B57A419EB312524"}}}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13396954478064418", "extension_telemetry_file_data": {}}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "session_restore_prompt": {"ignored": false}, "sessions": {"event_log": [{"crashed": false, "time": "13396951348649092", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396951515642558", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396952049040145", "type": 0}, {"crashed": true, "time": "13396952323527268", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13396952429103996", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396953704011808", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 5}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "last_pwilo_api_fetch_time": "*****************", "pcb_supported": true}, "signin": {"accounts_metadata_dict": {"000600001C1980E4": {"BookmarksExplicitBrowserSigninEnabled": false, "ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "signin_with_explicit_browser_signin_on": true}, "spellcheck": {"dictionaries": ["en-US"]}, "sync": {"has_been_enabled": true, "passwords_per_account_pref_migration_done": true}, "sync_consent_recorded": true, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "tab_groups": [], "toolbar": {"pinned_chrome_labs_migration_complete": true}, "toolbar_declutter": {"new_user_cleanup_triggered": true, "undo": {"last_time": "*****************"}}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}, "visual_search": {"dma_state": 1}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138", "link_handling_info": {"enabled_for_installed_apps": true}}, "zerosuggest": {"cachedresults_with_url": {"https://www.bing.com/search?q=world+capitals&search=&form=QBLH": "[\"\",[\"yunita ababil\",\"psg vs chelsea\",\"cara daftar ukppg 2025\",\"lita gading psikolog\",\"<PERSON><PERSON><PERSON> Bandung\",\"berita terkini\",\"i<PERSON><PERSON><PERSON> joko<PERSON>\",\"jadwal motogp hari ini\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bcp\":false,\"phi\":0,\"tlw\":false},\"google:suggestdetail\":[{\"q\":\"qs=PN\\u0026sc=8-0\"},{\"q\":\"qs=PN\\u0026sk=PN1\\u0026sc=8-0\"},{\"q\":\"qs=PN\\u0026sk=PN2\\u0026sc=8-0\"},{\"q\":\"qs=PN\\u0026sk=PN3\\u0026sc=8-0\"},{\"t\":\"Persib Bandung\",\"a\":\"Indonesian football club\",\"i\":\"https://th.bing.com/th/id/OSK.********************************?w=120\\u0026h=120\\u0026c=6\\u0026p=0\\u0026pid=RS\",\"q\":\"qs=MB\\u0026sk=PN4\\u0026sc=8-0\\u0026asbe=PN\\u0026filters=ufn%3a%22Persib+Bandung%22+sid%3a%220adb4704-d9e4-0c94-cde2-c66ce914ec17%22\"},{\"q\":\"qs=PN\\u0026sk=PN5\\u0026sc=8-0\"},{\"q\":\"qs=PN\\u0026sk=PN6\\u0026sc=8-0\"},{\"q\":\"qs=PN\\u0026sk=PN7\\u0026sc=8-0\"}],\"google:suggestrelevance\":[99,99,99,99,99,99,99,99],\"google:suggestsubtypes\":[[143],[143],[143],[143],[143],[143],[143],[143]],\"google:suggesttype\":[\"TRENDING_NOW\",\"TRENDING_NOW\",\"TRENDING_NOW\",\"TRENDING_NOW\",\"ENTITY\",\"TRENDING_NOW\",\"TRENDING_NOW\",\"TRENDING_NOW\"],\"google:verbatimrelevance\":799}]"}}}