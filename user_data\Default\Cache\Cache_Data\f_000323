{"v": "5.6.5", "fr": 24, "ip": 0, "op": 48, "w": 125, "h": 129, "nm": "Tile", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Bee 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [0], "e": [77]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [77], "e": [68]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 31, "s": [68], "e": [86.846]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [86.846], "e": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [60], "e": [0]}, {"t": 48}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [255, 254, 0], "e": [413, 173, 0], "to": [0, 0, 0], "ti": [-13, 78, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12.351, "s": [413, 173, 0], "e": [337.556, 77.615, 0], "to": [5.127, -30.762, 0], "ti": [115.766, 14.231, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19.057, "s": [337.556, 77.615, 0], "e": [246, 126, 0], "to": [-21.129, -2.597, 0], "ti": [30.869, -0.309, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.673, "s": [246, 126, 0], "e": [124.451, 87.133, 0], "to": [-38.328, 0.383, 0], "ti": [30.213, -10.381, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29.362, "s": [124.451, 87.133, 0], "e": [64.532, 152.147, 0], "to": [-42.997, 14.774, 0], "ti": [-0.312, -25.341, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.519, "s": [64.532, 152.147, 0], "e": [256, 255, 0], "to": [0.613, 49.73, 0], "ti": [-140.337, -2.143, 0]}, {"t": 44}]}, "a": {"a": 0, "k": [37.5, 28.5, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 4, "s": [80, 80, 100], "e": [150, 150, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 11, "s": [150, 150, 100], "e": [150, 150, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 37, "s": [150, 150, 100], "e": [80, 80, 100]}, {"t": 44}]}}, "ao": 1, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.438, 0], [0.577, -0.625], [-1.372, -2.917], [-3.311, -3.06], [-3.205, -1.209], [-1.095, 1.185], [1.372, 2.917], [3.311, 3.06]], "o": [[-1.107, 0], [-1.095, 1.185], [1.458, 3.099], [3.312, 3.059], [3.016, 1.137], [1.095, -1.185], [-1.458, -3.099], [-5.003, -4.622]], "v": [[-9.632, -11.278], [-12.204, -10.359], [-11.766, -3.904], [-4.37, 5.646], [5.736, 12.264], [12.204, 12.189], [11.766, 5.735], [4.37, -3.816]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.425, 0], [1.596, 0.602], [3.405, 3.145], [1.512, 3.213], [-1.405, 1.521], [-7.255, -6.702], [-1.512, -3.212], [1.405, -1.52]], "o": [[-1.215, 0], [-3.323, -1.253], [-3.403, -3.144], [-1.577, -3.353], [2.833, -3.065], [3.403, 3.145], [1.577, 3.353], [-0.759, 0.821]], "v": [[9.624, 14.102], [5.383, 13.201], [-5.049, 6.38], [-12.671, -3.479], [-12.939, -11.036], [5.049, -4.549], [12.672, 5.309], [12.939, 12.867]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.57599995931, 0.57599995931, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.422, 18.349]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.601, -2.815], [-6.942, -6.414], [-2.601, 2.816], [6.942, 6.414]], "o": [[-2.601, 2.815], [6.943, 6.414], [2.601, -2.815], [-6.943, -6.413]], "v": [[-12.571, -11.614], [-4.71, 5.098], [12.571, 11.613], [4.71, -5.099]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.422, 19.266]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.482, -2.931], [-1.811, -0.734], [-2.451, 0.403], [-1.548, 1.334], [0.226, 1.374], [4.977, -0.816]], "o": [[0.227, 1.373], [1.894, 0.767], [2.454, -0.404], [1.48, -1.276], [-0.483, -2.93], [-4.975, 0.821]], "v": [[-8.88, 1.834], [-5.721, 5.101], [1.018, 5.663], [7.223, 2.968], [9.167, -1.14], [-0.732, -4.969]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.852, 0], [1.372, 0.556], [0.285, 1.731], [-5.519, 0.91], [-0.573, -3.475], [1.754, -1.512], [2.632, -0.432]], "o": [[-1.695, 0], [-2.146, -0.869], [-0.573, -3.475], [5.521, -0.911], [0.285, 1.731], [-1.686, 1.452], [-0.88, 0.146]], "v": [[-1.426, 6.866], [-6.096, 6.027], [-9.867, 1.996], [-0.896, -5.955], [10.154, -1.302], [7.876, 3.726], [1.182, 6.649]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.57599995931, 0.57599995931, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.312, 32.494]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.528, -3.208], [-5.256, 0.866], [0.528, 3.209], [5.256, -0.866]], "o": [[0.529, 3.209], [5.256, -0.865], [-0.529, -3.208], [-5.256, 0.866]], "v": [[-9.517, 1.568], [0.957, 5.809], [9.518, -1.568], [-0.957, -5.809]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.455, 32.841]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.105, 0], [0.359, -0.01], [3.141, -1.367], [-0.041, -1.612], [-3.018, -1.135], [-4.509, 0.109], [-3.14, 1.367], [0.041, 1.612], [3.017, 1.137]], "o": [[-0.355, 0], [-4.508, 0.113], [-2.956, 1.287], [0.04, 1.612], [3.205, 1.206], [4.507, -0.114], [2.956, -1.286], [-0.04, -1.612], [-2.95, -1.11]], "v": [[0.911, -6.4], [-0.162, -6.385], [-12.022, -4.09], [-16.609, 0.471], [-11.798, 4.794], [0.162, 6.491], [12.021, 4.195], [16.609, -0.365], [11.798, -4.69]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.363, 0], [3.06, 1.151], [0.052, 2.07], [-3.398, 1.477], [-4.632, 0.117], [-3.323, -1.252], [-0.052, -2.071], [3.397, -1.479], [4.633, -0.117]], "o": [[-4.225, 0], [-3.467, -1.306], [-0.052, -2.071], [3.256, -1.418], [4.623, -0.118], [3.468, 1.306], [0.052, 2.07], [-3.256, 1.417], [-0.367, 0.008]], "v": [[-0.909, 7.503], [-12.151, 5.732], [-17.609, 0.497], [-12.421, -5.007], [-0.188, -7.385], [12.15, -5.626], [17.609, -0.391], [12.421, 5.113], [0.187, 7.491]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.57599995931, 0.57599995931, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [56.248, 34.595]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.096, -3.832], [9.45, -0.237], [0.096, 3.832], [-9.449, 0.238]], "o": [[0.097, 3.831], [-9.449, 0.238], [-0.096, -3.832], [9.449, -0.238]], "v": [[17.108, -0.43], [0.174, 6.938], [-17.109, 0.431], [-0.175, -6.938]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [56.249, 34.647]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.666, 0], [0.626, -0.513], [-0.271, -1.935], [-1.576, -1.922], [-1.93, -0.673], [-1.074, 0.882], [0.271, 1.934], [1.576, 1.922], [1.93, 0.673]], "o": [[-0.929, 0], [-1.077, 0.882], [0.282, 2.024], [1.577, 1.923], [1.845, 0.647], [1.077, -0.882], [-0.282, -2.025], [-1.577, -1.923], [-0.77, -0.269]], "v": [[-3.438, -7.693], [-5.799, -6.919], [-7.049, -2.55], [-4.167, 3.568], [1.27, 7.594], [5.799, 7.223], [7.049, 2.855], [4.166, -3.264], [-1.272, -7.289]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.158, 0], [0.879, 0.308], [1.691, 2.061], [0.308, 2.203], [-1.356, 1.112], [-2.185, -0.764], [-1.691, -2.063], [-0.308, -2.203], [1.356, -1.112]], "o": [[-0.78, 0], [-2.102, -0.733], [-1.691, -2.063], [-0.32, -2.293], [1.357, -1.111], [2.101, 0.733], [1.691, 2.062], [0.32, 2.293], [-0.81, 0.665]], "v": [[3.442, 8.997], [0.942, 8.538], [-4.94, 4.204], [-8.04, -2.412], [-6.434, -7.693], [-0.942, -8.233], [4.939, -3.898], [8.04, 2.717], [6.433, 7.997]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.57599995931, 0.57599995931, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.762, 43.355]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.515, -2.062], [3.378, 4.12], [-2.515, 2.062], [-3.379, -4.12]], "o": [[-2.514, 2.063], [-3.378, -4.119], [2.513, -2.063], [3.377, 4.119]], "v": [[6.116, 7.458], [-4.553, 3.733], [-6.116, -7.458], [4.553, -3.733]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.762, 43.507]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0.726, -0.548], [0, 0], [0.059, 1]], "o": [[-0.704, 0.713], [0, 0], [-0.184, -0.891], [0, 0]], "v": [[8.728, 2.34], [6.576, 4.236], [-8.361, -1.392], [-8.728, -4.236]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.725, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [28.259, 48.011]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0.521, -0.674], [0, 0], [-0.138, 0.878]], "o": [[-0.477, 0.751], [0, 0], [0.054, -0.85], [0, 0]], "v": [[10.367, 2.79], [8.864, 4.918], [-10.367, -2.328], [-10.092, -4.918]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.725, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.899, 43.408]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0.385, -0.737], [0, 0], [-0.281, 0.813]], "o": [[-0.324, 0.797], [0, 0], [0.195, -0.809], [0, 0]], "v": [[11.146, 2.914], [10.074, 5.217], [-11.146, -2.778], [-10.434, -5.217]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.725, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [31.462, 38.767]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.271, -0.816], [0, 0], [-0.386, 0.736], [0, 0]], "o": [[0, 0], [0.334, -0.792], [0, 0], [-0.195, 0.808]], "v": [[10.436, 5.203], [-11.123, -2.92], [-10.033, -5.203], [11.123, 2.768]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.725, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.051, 34.116]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.127, -0.883], [0, 0], [-0.541, 0.666], [0, 0]], "o": [[0, 0], [0.487, -0.746], [0, 0], [-0.033, 0.857]], "v": [[10.043, 4.893], [-10.289, -2.768], [-8.751, -4.893], [10.289, 2.281]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.725, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.608, 29.476]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.068, -1.004], [0, 0], [-0.747, 0.54], [0, 0]], "o": [[0, 0], [0.715, -0.709], [0, 0], [0.205, 0.898]], "v": [[8.59, 4.168], [-8.59, -2.305], [-6.389, -4.168], [8.167, 1.315]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.725, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.266, 24.878]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.19, 1.202], [1.389, -2.64], [0.524, -1.392], [-1.139, -1.595], [1.091, -0.777], [0.715, -0.711], [0.656, -0.807], [0.538, -0.826], [0.431, -0.806], [0.312, -0.74], [0.174, -0.462], [0.121, -0.352], [0.212, -0.876], [0.131, -0.841], [0.063, -0.983], [-0.056, -0.977], [-0.196, -0.953], [-3.163, -1.191], [-3.542, 2.648], [-0.733, 0.743], [-0.552, 0.712], [-0.45, 0.709], [-0.42, 0.809], [-0.322, 0.794], [-0.136, 0.361], [-0.147, 0.459], [-0.226, 0.937], [-0.121, 0.844], [-0.033, 0.861], [0.071, 0.875], [0.23, 1.003], [0.553, 1.052], [-0.571, 1.516], [0.954, 1.511]], "o": [[-3.19, -1.202], [-1.715, 0.506], [-0.572, 1.516], [-1.109, 0.426], [-0.841, 0.608], [-0.62, 0.601], [-0.543, 0.67], [-0.465, 0.712], [-0.452, 0.861], [-0.188, 0.433], [-0.135, 0.36], [-0.279, 0.806], [-0.218, 0.879], [-0.13, 0.83], [-0.055, 0.901], [0.06, 1.045], [0.915, 4.332], [3.163, 1.192], [0.78, -0.589], [0.688, -0.699], [0.603, -0.782], [0.456, -0.716], [0.416, -0.794], [0.141, -0.34], [0.174, -0.462], [0.251, -0.752], [0.21, -0.898], [0.14, -0.975], [0.04, -1.039], [-0.068, -0.992], [-0.308, -1.309], [1.909, -0.447], [0.524, -1.392], [0.698, -2.9]], "v": [[9.627, -23.892], [1.577, -21.285], [-1.878, -18.365], [-0.967, -13.526], [-4.274, -11.719], [-6.617, -9.734], [-8.518, -7.637], [-10.124, -5.415], [-11.475, -3.128], [-12.609, -0.752], [-13.16, 0.612], [-13.537, 1.678], [-14.279, 4.215], [-14.805, 6.809], [-15.092, 9.503], [-15.092, 12.332], [-14.705, 15.34], [-8.382, 23.902], [2.016, 21.645], [4.295, 19.638], [6.161, 17.512], [7.725, 15.297], [9.044, 12.997], [10.159, 10.601], [10.577, 9.555], [11.065, 8.158], [11.776, 5.643], [12.273, 3.022], [12.531, 0.294], [12.486, -2.551], [12.038, -5.564], [10.745, -9.113], [14.624, -12.146], [13.956, -16.621]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-2.844, -1.072], [0.905, -2.473], [0.458, -1.213], [2.283, -0.053], [-0.431, -1.83], [-0.068, -1.004], [0.037, -0.929], [0.127, -0.883], [0.197, -0.844], [0.271, -0.816], [0.167, -0.444], [0.139, -0.334], [0.386, -0.736], [0.445, -0.702], [0.52, -0.674], [0.607, -0.617], [0.726, -0.548], [2.899, 1.092], [0.799, 3.779], [0.058, 1], [-0.057, 0.921], [-0.138, 0.878], [-0.208, 0.839], [-0.282, 0.812], [-0.131, 0.349], [-0.184, 0.426], [-0.386, 0.736], [-0.456, 0.699], [-0.541, 0.666], [-0.628, 0.609], [-0.747, 0.54], [-1.557, 0.326], [-0.597, 1.584], [-1.555, 0.338]], "o": [[2.842, 1.071], [0.946, 1.28], [-0.596, 1.584], [0.955, 1.271], [0.205, 0.899], [0.071, 0.873], [-0.033, 0.857], [-0.118, 0.825], [-0.196, 0.807], [-0.142, 0.442], [-0.132, 0.348], [-0.324, 0.797], [-0.398, 0.768], [-0.477, 0.751], [-0.565, 0.729], [-0.704, 0.713], [-3.094, 2.312], [-2.898, -1.091], [-0.184, -0.891], [-0.049, -0.864], [0.054, -0.85], [0.128, -0.821], [0.195, -0.809], [0.116, -0.342], [0.167, -0.444], [0.335, -0.792], [0.408, -0.764], [0.487, -0.746], [0.586, -0.722], [0.714, -0.708], [1.532, -1.091], [-1.68, -1.546], [0.457, -1.213], [0.952, -2.456]], "v": [[9.229, -22.833], [12.719, -16.428], [13.565, -12.546], [8.848, -10.013], [10.938, -5.314], [11.359, -2.461], [11.401, 0.249], [11.155, 2.863], [10.678, 5.378], [9.991, 7.812], [9.521, 9.157], [9.111, 10.174], [8.04, 12.477], [6.771, 14.692], [5.268, 16.82], [3.491, 18.844], [1.339, 20.74], [-7.983, 22.844], [-13.598, 15.112], [-13.964, 12.268], [-13.964, 9.574], [-13.688, 6.984], [-13.18, 4.482], [-12.467, 2.044], [-12.102, 1.01], [-11.568, -0.312], [-10.479, -2.594], [-9.177, -4.799], [-7.639, -6.924], [-5.82, -8.934], [-3.619, -10.797], [1.053, -12.951], [-0.82, -17.966], [2.379, -20.324]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.57599995931, 0.57599995931, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.496, 31.507]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.976, 1.121], [0.873, 4.13], [0.059, 1.014], [-0.053, 0.878], [-0.127, 0.809], [-0.215, 0.869], [-0.275, 0.793], [-0.127, 0.338], [-0.188, 0.433], [-0.439, 0.835], [-0.456, 0.697], [-0.53, 0.653], [-0.615, 0.595], [-0.81, 0.587], [-1.308, 0.404], [-0.573, 1.522], [-1.665, 0.432], [-3.006, -1.134], [0.793, -2.687], [0.483, -1.282], [2.11, -0.282], [-0.364, -1.546], [-0.066, -0.975], [0.039, -1.017], [0.135, -0.94], [0.204, -0.875], [0.249, -0.749], [0.171, -0.454], [0.141, -0.339], [0.413, -0.788], [0.448, -0.706], [0.589, -0.763], [0.671, -0.68], [0.757, -0.572]], "o": [[-2.977, -1.122], [-0.192, -0.929], [-0.053, -0.954], [0.063, -0.971], [0.129, -0.824], [0.207, -0.857], [0.129, -0.374], [0.171, -0.454], [0.306, -0.724], [0.426, -0.796], [0.528, -0.81], [0.641, -0.789], [0.689, -0.685], [1.298, -0.924], [-1.399, -1.604], [0.482, -1.281], [1.177, -2.542], [3.009, 1.133], [0.967, 1.42], [-0.573, 1.521], [0.716, 1.168], [0.224, 0.981], [0.068, 0.844], [-0.032, 0.834], [-0.118, 0.824], [-0.223, 0.925], [-0.144, 0.446], [-0.133, 0.354], [-0.312, 0.768], [-0.41, 0.79], [-0.444, 0.698], [-0.537, 0.695], [-0.712, 0.722], [-3.384, 2.53]], "v": [[-8.18, 23.374], [-14.149, 15.229], [-14.527, 12.301], [-14.527, 9.54], [-14.245, 6.896], [-13.727, 4.346], [-13, 1.858], [-12.63, 0.812], [-12.085, -0.535], [-10.977, -2.856], [-9.648, -5.106], [-8.076, -7.28], [-6.211, -9.337], [-3.949, -11.255], [-0.029, -13.252], [-1.348, -18.165], [1.973, -20.808], [9.428, -23.361], [13.346, -16.522], [14.097, -12.345], [9.864, -9.525], [11.489, -5.443], [11.925, -2.498], [11.968, 0.271], [11.716, 2.944], [11.23, 5.506], [10.529, 7.99], [10.05, 9.357], [9.636, 10.391], [8.542, 12.739], [7.25, 14.995], [5.716, 17.166], [3.895, 19.24], [1.682, 21.19]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.156999999402, 0.156999999402, 0.156999999402, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.494, 31.506]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.026, 0], [0.039, 0.245], [0, 0], [0, 0], [-0.174, 0.215], [-0.215, -0.174], [0, 0], [0, 0], [0.273, -0.044]], "o": [[-0.24, 0], [0, 0], [0, 0], [-0.215, -0.173], [0.173, -0.214], [0, 0], [0, 0], [0.045, 0.273], [-0.027, 0.004]], "v": [[2.084, 4.908], [1.592, 4.488], [0.618, -1.545], [-2.373, -3.956], [-2.449, -4.659], [-1.745, -4.734], [1.544, -2.083], [2.577, 4.328], [2.165, 4.902]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.451000019148, 0.451000019148, 0.451000019148, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.115, 5.158]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.112, 0], [0.099, 0.12], [-0.213, 0.176], [0, 0], [0, 0], [0.01, -0.276], [0.301, 0.007], [0, 0], [0, 0]], "o": [[-0.144, 0], [-0.176, -0.213], [0, 0], [0, 0], [0.276, 0.012], [-0.012, 0.275], [0, 0], [0, 0], [-0.092, 0.076]], "v": [[-4.419, 2.51], [-4.805, 2.328], [-4.738, 1.624], [0.27, -2.51], [4.493, -2.332], [4.972, -1.811], [4.45, -1.333], [0.611, -1.494], [-4.101, 2.396]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.451000019148, 0.451000019148, 0.451000019148, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [48.45, 8.999]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18", "bm": 0, "hd": false}], "ip": 0, "op": 48, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Dash", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [250, 250, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-13, 78], [115.766, 14.231], [30.869, -0.309], [30.213, -10.381], [-0.312, -25.341], [-140.337, -2.143]], "o": [[0, 0], [5.127, -30.762], [-21.129, -2.597], [-38.328, 0.383], [-42.997, 14.774], [0.613, 49.73], [262, 4]], "v": [[5, 4], [163, -77], [87.556, -172.385], [-4, -124], [-125.549, -162.867], [-185.468, -97.853], [6, 5]], "c": false}, "x": "var $bm_rt;\n$bm_rt = content('Shape 1').content('Path 1').path;"}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.576470588235, 0.576470588235, 0.576470588235, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 8}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 20}}, {"n": "o", "nm": "offset", "v": {"a": 0, "k": 0}}], "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [0], "e": [23]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [23], "e": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [50], "e": [74]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [74], "e": [100]}, {"t": 44}]}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [0], "e": [28]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [28], "e": [51]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 27, "s": [51], "e": [79]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [79], "e": [100]}, {"t": 44}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}], "ip": 0, "op": 48, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Flower", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0], "e": [20]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 21, "s": [20], "e": [0]}, {"t": 42}]}, "p": {"a": 0, "k": [250, 250, 0]}, "a": {"a": 0, "k": [217.5, 221, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.5, 0.5, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100], "e": [60, 60, 100]}, {"i": {"x": [0.5, 0.5, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.333], "y": [0, 0, 0]}, "t": 8, "s": [60, 60, 100], "e": [65, 65, 100]}, {"i": {"x": [0.5, 0.5, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.333], "y": [0, 0, 0]}, "t": 12, "s": [65, 65, 100], "e": [65, 65, 100]}, {"i": {"x": [0.5, 0.5, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.333], "y": [0, 0, 0]}, "t": 30, "s": [65, 65, 100], "e": [60, 60, 100]}, {"i": {"x": [0.5, 0.5, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.333], "y": [0, 0, 0]}, "t": 34, "s": [60, 60, 100], "e": [100, 100, 100]}, {"t": 42}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-13.972, 38.386], [0, 0], [13.972, -38.386], [-15.661, -5.701]], "o": [[13.971, -38.386], [0, 0], [-13.971, 38.386], [15.662, 5.7]], "v": [[28.358, 16.868], [28.717, -72.354], [-28.358, -3.775], [-21.877, 66.654]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.948999980852, 0.313999998803, 0.13300000359, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [263.984, 85.896]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[13.972, -38.386], [0, 0], [-13.971, 38.385], [15.661, 5.7]], "o": [[-13.971, 38.386], [0, 0], [13.972, -38.386], [-15.661, -5.701]], "v": [[-28.358, -16.868], [-28.718, 72.355], [28.357, 3.775], [21.877, -66.653]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.948999980852, 0.313999998803, 0.13300000359, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [170.647, 355.432]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-26.257, 31.292], [0, 0], [26.257, -31.293], [-12.768, -10.713]], "o": [[26.258, -31.292], [0, 0], [-26.258, 31.292], [12.767, 10.713]], "v": [[23.073, 21.701], [53.927, -62.016], [-23.161, -17.093], [-41.159, 51.304]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.423999980852, 0.255000005984, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [305.067, 113.833]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[26.258, -31.293], [0, 0], [-26.258, 31.293], [12.767, 10.713]], "o": [[-26.257, 31.292], [0, 0], [26.257, -31.292], [-12.767, -10.713]], "v": [[-23.073, -21.702], [-53.927, 62.017], [23.162, 17.093], [41.16, -51.303]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.423999980852, 0.255000005984, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [129.562, 327.494]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-35.377, 20.425], [0, 0], [35.377, -20.425], [-8.334, -14.434]], "o": [[35.377, -20.425], [0, 0], [-35.377, 20.425], [8.333, 14.433]], "v": [[10.595, 26.134], [68.222, -41.982], [-19.583, -26.134], [-59.888, 31.983]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.948999980852, 0.313999998803, 0.13300000359, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [339.978, 152.439]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[35.377, -20.425], [0, 0], [-35.377, 20.424], [8.333, 14.435]], "o": [[-35.376, 20.424], [0, 0], [35.376, -20.425], [-8.334, -14.433]], "v": [[-10.596, -26.134], [-68.222, 41.982], [19.582, 26.135], [59.888, -31.983]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.948999980852, 0.313999998803, 0.13300000359, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [94.653, 288.889]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-40.229, 7.094], [0, 0], [40.228, -7.093], [-2.894, -16.414]], "o": [[40.229, -7.093], [0, 0], [-40.23, 7.094], [2.894, 16.413]], "v": [[-3.16, 29.718], [74.288, -14.581], [-13.64, -29.719], [-71.393, 11.108]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847000002394, 0.231000010173, 0.004000000393, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [360.093, 196.97]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[40.229, -7.093], [0, 0], [-40.229, 7.094], [2.894, 16.413]], "o": [[-40.229, 7.093], [0, 0], [40.228, -7.093], [-2.894, -16.414]], "v": [[3.161, -29.719], [-74.288, 14.58], [13.641, 29.718], [71.394, -11.107]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847000002394, 0.231000010173, 0.004000000393, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [74.537, 244.358]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-40.229, -7.093], [0, 0], [40.229, 7.093], [2.895, -16.414]], "o": [[40.229, 7.094], [0, 0], [-40.229, -7.093], [-2.893, 16.413]], "v": [[-13.641, 29.718], [74.288, 14.579], [-3.161, -29.719], [-71.395, -11.107]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.423999980852, 0.255000005984, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [360.093, 244.358]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[40.229, 7.093], [0, 0], [-40.228, -7.094], [-2.894, 16.413]], "o": [[-40.229, -7.094], [0, 0], [40.229, 7.093], [2.894, -16.413]], "v": [[13.642, -29.719], [-74.288, -14.579], [3.161, 29.719], [71.394, 11.108]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.423999980852, 0.255000005984, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [74.537, 196.969]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-35.377, -20.425], [0, 0], [35.377, 20.424], [8.333, -14.434]], "o": [[35.377, 20.425], [0, 0], [-35.377, -20.424], [-8.334, 14.434]], "v": [[-19.582, 26.134], [68.223, 41.982], [10.595, -26.135], [-59.889, -31.983]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847000002394, 0.231000010173, 0.004000000393, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [339.978, 288.889]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[35.376, 20.425], [0, 0], [-35.376, -20.425], [-8.333, 14.433]], "o": [[-35.377, -20.425], [0, 0], [35.377, 20.425], [8.333, -14.434]], "v": [[19.583, -26.135], [-68.221, -41.982], [-10.595, 26.135], [59.889, 31.982]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847000002394, 0.231000010173, 0.004000000393, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [94.653, 152.439]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-26.258, -31.292], [0, 0], [26.258, 31.292], [12.768, -10.714]], "o": [[26.258, 31.293], [0, 0], [-26.256, -31.293], [-12.768, 10.712]], "v": [[-23.161, 17.094], [53.928, 62.017], [23.072, -21.702], [-41.16, -51.302]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.948999980852, 0.313999998803, 0.13300000359, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [305.067, 327.494]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[26.257, 31.292], [0, 0], [-26.257, -31.292], [-12.767, 10.713]], "o": [[-26.258, -31.292], [0, 0], [26.258, 31.293], [12.768, -10.712]], "v": [[23.161, -17.094], [-53.928, -62.016], [-23.074, 21.701], [41.159, 51.303]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.948999980852, 0.313999998803, 0.13300000359, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [129.563, 113.833]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-13.971, -38.386], [0, 0], [13.971, 38.386], [15.663, -5.701]], "o": [[13.972, 38.386], [0, 0], [-13.972, -38.385], [-15.661, 5.7]], "v": [[-28.358, 3.774], [28.717, 72.354], [28.358, -16.869], [-21.878, -66.654]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.423999980852, 0.255000005984, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [263.984, 355.431]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[13.971, 38.386], [0, 0], [-13.972, -38.387], [-15.662, 5.7]], "o": [[-13.971, -38.386], [0, 0], [13.971, 38.385], [15.661, -5.7]], "v": [[28.358, -3.774], [-28.717, -72.354], [-28.357, 16.869], [21.878, 66.654]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.423999980852, 0.255000005984, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [170.646, 85.896]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -40.849], [0, 0], [0, 40.85], [16.667, 0]], "o": [[0, 40.85], [0, 0], [0, -40.849], [-16.667, 0]], "v": [[-30.177, -10], [0.001, 73.964], [30.177, -10], [0.001, -73.964]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847000002394, 0.231000010173, 0.004000000393, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [217.315, 367.113]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 40.85], [0, 0], [0, -40.85], [-16.666, 0]], "o": [[0, -40.85], [0, 0], [0, 40.85], [16.667, 0]], "v": [[30.178, 10], [-0.001, -73.964], [-30.178, 10], [-0.001, 73.964]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847000002394, 0.231000010173, 0.004000000393, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [217.315, 74.214]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 35.784], [35.784, 0], [0, -35.784], [-35.784, 0]], "o": [[0, -35.784], [-35.784, 0], [0, 35.784], [35.784, 0]], "v": [[64.793, 0], [0, -64.793], [-64.793, 0], [0, 64.793]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.187999994615, 0.113999998803, 0.097999999102, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [217.315, 220.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 19", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 47.695], [47.695, 0], [0, -47.695], [-47.696, 0]], "o": [[0, -47.695], [-47.696, 0], [0, 47.695], [47.695, 0]], "v": [[86.36, 0], [0, -86.36], [-86.36, 0], [0, 86.36]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.610000011968, 0.156000010173, 0.002999999944, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [217.316, 220.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 20", "bm": 0, "hd": false}], "ip": 0, "op": 48, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 5, "ty": 0, "nm": "Flower", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [62, 64, 0]}, "a": {"a": 0, "k": [250, 250, 0]}, "s": {"a": 0, "k": [25, 25, 100]}}, "ao": 0, "w": 500, "h": 500, "ip": 0, "op": 48, "st": 0, "bm": 0}], "markers": []}