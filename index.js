const { chromium, firefox, webkit } = require('playwright');
const path = require('path');
const fs = require('fs');

// Basic logging setup (can be expanded with a proper logging library like <PERSON> or Pi<PERSON>)
const logFilePath = path.join(__dirname, 'ms_rewards_automation.log');
const logStream = fs.createWriteStream(logFilePath, { flags: 'a' });

function log(level, message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
    process.stdout.write(logMessage); // Log to console
    logStream.write(logMessage); // Log to file
}

class MicrosoftRewardsBot {
    constructor() {
        this.browser = null;
        this.page = null;
        this.context = null; // Tambahkan ini
        this.userDataDir = path.join(__dirname, 'user_data');
        this.base_url = "https://rewards.microsoft.com/";
        this.bing_url = "https://www.bing.com/";
        this.desktopSearchCount = 30; // Example value, adjust as needed
        this.mobileSearchCount = 20;  // Example value, adjust as needed
        this.searchTerms = [
            "weather forecast", "latest news", "sports scores", "cooking recipes", "travel destinations",
            "technology trends", "science facts", "history events", "famous quotes", "book reviews",
            "movie releases", "music charts", "art exhibitions", "fashion styles", "health tips",
            "fitness routines", "car models", "space exploration", "animal facts", "plant types",
            "world capitals", "currency exchange rates", "stock market news", "programming languages", "web development",
            "artificial intelligence", "machine learning", "data science", "cyber security", "cloud computing"
        ];

        // Ensure user data directory exists
        if (!fs.existsSync(this.userDataDir)) {
            fs.mkdirSync(this.userDataDir, { recursive: true });
        }
    }

    async launchBrowser() {
        log('info', 'Launching browser...');
        try {
            // Menggunakan launchPersistentContext alih-alih launch
            const context = await chromium.launchPersistentContext(this.userDataDir, {
                headless: false, // Set to true for headless mode
                channel: 'msedge', // Specify Microsoft Edge
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-sync',
                    '--disable-features=site-per-process',
                    '--disable-blink-features=AutomationControlled' // Helps prevent detection
                ]
            });
            
            this.browser = context.browser();
            this.page = await context.newPage();
            this.context = context; // Simpan referensi ke context
            
            log('info', 'Browser launched successfully.');
        } catch (error) {
            log('error', `Failed to launch browser: ${error.message}`);
            throw error;
        }
    }

    async closeBrowser() {
        if (this.browser) {
            log('info', 'Closing browser...');
            await this.browser.close();
            this.browser = null;
            this.page = null;
            log('info', 'Browser closed.');
        }
    }

    async waitForSelectorWithTimeout(selector, timeout = 5000, visible = true) {
        try {
            const state = visible ? 'visible' : 'attached';
            return await this.page.waitForSelector(selector, { state, timeout });
        } catch (error) {
            return null; // Return null instead of throwing
        }
    }

    async waitForPageLoad(timeout = 30000) {
        try {
            await this.page.waitForLoadState('domcontentloaded', { timeout });
            await this.page.waitForLoadState('networkidle', { timeout: 5000 }).catch(() => {
                // Ignore networkidle timeout, continue anyway
                log('debug', 'Network did not become idle, continuing anyway');
            });
            return true;
        } catch (error) {
            log('warning', `Page load timeout: ${error.message}`);
            return false;
        }
    }

    // Placeholder for other methods to be migrated
    async dismissBanners() {
        log('info', 'Attempting to dismiss banners...');
        const bannerXPaths = [
            "//button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]",
            "//span[contains(@class, 'cancel') or contains(@class, 'close')]",
            "//a[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close')]",
            "//button[contains(@id, 'declineButton') or contains(@id, 'closeButton')]",
            "//div[contains(@class, 'lightbox-close')]",
            "//div[contains(@class, 'common-banner-close')]",
            "//button[contains(@class, 'notification-close')]",
            "//button[contains(@class, 'close-button')]",
            "//button[contains(@class, 'glif-dismiss')]",
            "//button[contains(@class, 'me-Close')]",
            "//button[contains(@class, 'ub-emb-close')]",
            "//button[contains(@class, 'ot-close-icon')]",
            "//button[contains(@class, 'c-action-trigger') and contains(@aria-label, 'Close')]",
            "//button[contains(@class, 'f-button') and contains(text(), 'No thanks')]",
            "//button[contains(@class, 'cookie-consent-button') and contains(text(), 'Accept')]",
            "//button[contains(@class, 'cookie-button') and contains(text(), 'Accept')]",
            "//button[contains(@id, 'onetrust-accept-btn-handler')]",
            "//a[contains(@id, 'RSTC_Close')]",
            "//button[contains(@id, 'RSTC_Close')]"
        ];

        // Coba semua XPath dengan timeout yang lebih pendek
        for (const xpath of bannerXPaths) {
            try {
                // Gunakan timeout yang lebih pendek (1000ms) untuk mempercepat proses
                const elements = await this.page.$$(xpath);
                for (const element of elements) {
                    // Periksa apakah elemen terlihat
                    const isVisible = await element.isVisible();
                    if (isVisible) {
                        log('info', `Dismissing banner with XPath: ${xpath}`);
                        await element.click().catch(e => log('debug', `Click failed: ${e.message}`));
                        await this.page.waitForTimeout(500); // Tunggu sebentar setelah klik
                        break; // Keluar dari loop jika berhasil mengklik
                    }
                }
            } catch (error) {
                // Lanjutkan ke XPath berikutnya jika terjadi error
                log('debug', `Error with XPath: ${xpath}: ${error.message}`);
            }
        }
        log('info', 'Finished attempting to dismiss banners.');
    }

    async checkLoginStatus() {
        log('info', 'Checking login status...');
        const pointsXPaths = [
            "//mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span",
            "//mee-rewards-user-status-banner//mee-rewards-counter-animation/span",
            "//div[contains(@class, 'points-package')]//span[contains(@class, 'points-label')]"
        ];

        for (const xpath of pointsXPaths) {
            try {
                await this.page.waitForSelector(xpath, { state: 'visible', timeout: 10000 });
                log('info', 'Login status: Logged in (points element found).');
                return true;
            } catch (error) {
                log('debug', `Points element not found with XPath: ${xpath}. Trying next.`);
            }
        }
        log('info', 'Login status: Not logged in (points element not found).');
        return false;
    }

    async login() {
        log('info', 'Attempting to log in...');
        await this.page.goto(this.base_url);
        await this.dismissBanners();

        if (await this.checkLoginStatus()) {
            log('info', 'Already logged in.');
            return true;
        }

        log('info', 'Navigating to Microsoft login page...');
        await this.page.goto('https://login.live.com/');
        await this.page.waitForLoadState('networkidle');

        // Wait for user to manually log in if necessary
        log('warn', 'Please log in manually if prompted. Waiting for login to complete...');
        try {
            await this.page.waitForFunction(() => {
                const pointsElement = document.evaluate("//mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                return pointsElement !== null && pointsElement.offsetParent !== null; // Check for visibility
            }, { timeout: 60000 }); // Wait up to 60 seconds for points element to appear
            log('info', 'Manual login detected. Points element found.');
            return true;
        } catch (error) {
            log('error', `Manual login timed out or failed: ${error.message}`);
            return false;
        }
    }

    async run() {
        try {
            await this.launchBrowser();
            if (await this.login()) {
                log('info', 'Checking initial points balance...');
                const initialPoints = await this.checkPointsBalance();
                log('info', `Initial points balance: ${initialPoints}`);

                await this.performSearches(this.desktopSearchCount, false);
                await this.performSearches(this.mobileSearchCount, true);
                await this.completeDailySet();
                await this.completeOtherActivities();
                
                log('info', 'Checking final points balance...');
                const finalPoints = await this.checkPointsBalance();
                log('info', `Final points balance: ${finalPoints}`);

                log('info', 'Automation workflow completed.');
            } else {
                log('error', 'Login failed. Automation workflow aborted.');
            }
        } catch (error) {
            log('error', `Automation workflow failed: ${error.message}`);
        } finally {
            if (this.context) {
                await this.context.close();
            }
        }
    }

    async performSearches(count, isMobile) {
        const deviceType = isMobile ? 'mobile' : 'desktop';
        log('info', `Starting ${count} ${deviceType} searches...`);

        if (isMobile) {
            await this.page.setViewportSize({ width: 375, height: 812 }); // iPhone X dimensions
            await this.page.setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1');
            log('info', 'Set user agent to mobile.');
        } else {
            await this.page.setViewportSize({ width: 1366, height: 768 }); // Standard desktop dimensions
            await this.page.setUserAgent(''); // Reset to default desktop
            log('info', 'Set user agent to desktop.');
        }

        for (let i = 0; i < count; i++) {
            const searchTerm = this.searchTerms[Math.floor(Math.random() * this.searchTerms.length)];
            log('info', `Performing ${deviceType} search ${i + 1}/${count} for: "${searchTerm}"`);

            try {
                await this.page.goto(this.bing_url);
                await this.dismissBanners();

                const searchBox = await this.page.waitForSelector('input[name="q"], textarea[name="q"]', { state: 'visible', timeout: 10000 });
                await searchBox.fill(searchTerm);
                await searchBox.press('Enter');

                await this.page.waitForLoadState('networkidle', { timeout: 15000 });
                await this.page.waitForTimeout(Math.random() * 3000 + 2000); // Random delay between 2-5 seconds

                // Scroll down to simulate user activity
                await this.page.evaluate(() => {
                    window.scrollBy(0, window.innerHeight / 2);
                });
                await this.page.waitForTimeout(1000);
                await this.page.evaluate(() => {
                    window.scrollBy(0, window.innerHeight / 2);
                });
                await this.page.waitForTimeout(1000);

            } catch (error) {
                log('error', `Search failed for "${searchTerm}": ${error.message}`);
            }
        }
        log('info', `Finished ${count} ${deviceType} searches.`);
    }

    async isDailySetItemComplete(cardElement) {
        try {
            // Check for green checkmark icon
            const checkmarkIcon = await cardElement.$("//span[contains(@class, 'mee-icon-SkypeCircleCheck')]");
            if (checkmarkIcon && await checkmarkIcon.isVisible()) {
                log('debug', 'Daily Set Item appears complete via green checkmark icon.');
                return true;
            }

            // Check for 'complete' attribute on ancestor mee-rewards-points
            const pointsParent = await cardElement.$("xpath=./ancestor::mee-rewards-points");
            if (pointsParent) {
                const completeAttr = await pointsParent.getAttribute('complete');
                if (completeAttr && completeAttr.toLowerCase() === 'true') {
                    log('debug', 'Daily Set Item appears complete via mee-rewards-points@complete=\'true\'.');
                    return true;
                }
            }

            // Check for 'state' attribute
            const stateAttr = await cardElement.getAttribute('state');
            if (stateAttr && stateAttr.toLowerCase() === 'complete') {
                log('debug', 'Daily Set Item appears complete via state attribute.');
                return true;
            }

            // Check for 'completed' class
            const completedClass = await cardElement.$("xpath=./*[contains(@class, 'completed')] | .[contains(@class, 'completed')]");
            if (completedClass && await completedClass.isVisible()) {
                log('debug', 'Daily Set Item appears complete via \'completed\' class.');
                return true;
            }

            return false;
        } catch (error) {
            log('debug', `Error checking daily set item completion status: ${error.message}. Assuming not complete.`);
            return false;
        }
    }

    async getDailySetItemStatus(cardElement) {
         try {
             if (await this.isDailySetItemComplete(cardElement)) {
                 return 'completed';
             }

             // Check for 'locked' status (blue lock icon or is-exclusive-locked-item)
             const lockIcon = await cardElement.$("//span[contains(@class, 'mee-icon-LockSolid')] | //span[contains(@class, 'mee-icon-LockFill')]");
             if (lockIcon && await lockIcon.isVisible()) {
                 log('debug', 'Daily Set Item appears locked via lock icon.');
                 return 'locked';
             }

             const isExclusiveLocked = await cardElement.$("xpath=./*[contains(@class, 'is-exclusive-locked-item')] | .[contains(@class, 'is-exclusive-locked-item')]");
             if (isExclusiveLocked && await isExclusiveLocked.isVisible()) {
                 log('debug', 'Daily Set Item appears locked via \'is-exclusive-locked-item\' class.');
                 return 'locked';
             }

             // Check for explicit disabling like aria-disabled="true" or 'locked-card' class
             const ariaDisabled = await cardElement.getAttribute('aria-disabled');
             if (ariaDisabled && ariaDisabled.toLowerCase() === 'true') {
                 log('debug', 'Daily Set Item appears explicitly disabled (aria-disabled=true).');
                 return 'locked';
             }

             const parentCardContainer = await cardElement.$("xpath=./ancestor::div[contains(@class, 'rewards-card-container')]");
             if (parentCardContainer) {
                 const ngClass = await parentCardContainer.getAttribute('ng-class');
                 if (ngClass && ngClass.includes("'locked-card'")) {
                     log('debug', 'Daily Set Item appears locked via parent ng-class \'locked-card\'.');
                     return 'locked';
                 }
             }

             return 'actionable';
         } catch (error) {
             log('warning', `Error checking daily set item status: ${error.message}. Assuming 'actionable'.`);
             return 'actionable';
         }
     }

    async completeDailySet() {
        log('info', 'Starting daily set activities...');
        await this.page.goto(this.base_url);
        await this.dismissBanners();

        const dailySetContainerXPath = "//*[@id='daily-set-section']";
        const cardClickableXPaths = [
            ".//div[contains(@class, 'rewards-card-container')]/a[contains(@class, 'ds-card-sec')]",
            ".//div[contains(@class, 'more-earning-card-item')]/a",
            ".//a[contains(@class, 'ds-card-sec')]",
            ".//div[contains(@class, 'rewards-card')]//mee-card",
            ".//div[contains(@class, 'promo-item')]//mee-card",
            ".//mee-card//a[contains(@href, '')]"
        ];

        let taskIdentifiers = [];

        try {
            const container = await this.page.waitForSelector(dailySetContainerXPath, { state: 'visible', timeout: 10000 });
            log('info', 'Found daily set container.');

            const allCandidateCards = await container.$$(
                cardClickableXPaths.map(xpath => `xpath=${xpath}`).join(' | ')
            );

            const visibleCards = [];
            for (const card of allCandidateCards) {
                if (await card.isVisible()) {
                    visibleCards.push(card);
                }
            }
            log('info', `Identified ${visibleCards.length} visible daily set cards.`);

            for (let i = 0; i < visibleCards.length; i++) {
                const card = visibleCards[i];
                const href = await card.getAttribute('href');
                const dataBiId = await card.getAttribute('data-bi-id');
                const dataMAttr = await card.getAttribute('data-m');

                let id = href || dataBiId || dataMAttr || `Unknown_DailySet_${i}`;
                if (id.length > 50) id = id.substring(0, 50) + '...';

                taskIdentifiers.push({
                    originalIndex: i,
                    id: id,
                    href: href,
                    dataBiId: dataBiId,
                    dataMAttr: dataMAttr
                });
            }
            log('info', `Collected ${taskIdentifiers.length} daily set task identifiers.`);

        } catch (error) {
            log('error', `Error during initial scan for daily set activities: ${error.message}. Skipping.`);
            return false;
        }

        const taskStatuses = {};
        taskIdentifiers.forEach(info => taskStatuses[info.originalIndex] = 'initial');

        for (const originalIndex of Object.keys(taskStatuses).sort((a, b) => a - b)) {
            const taskInfo = taskIdentifiers.find(item => item.originalIndex === parseInt(originalIndex));
            if (!taskInfo) continue;

            const offerId = taskInfo.id;
            log('info', `Processing daily set task (original index ${originalIndex}): '${offerId}'...`);

            let taskProcessedSuccessfully = false;
            const maxRetries = 3;

            for (let retryCount = 0; retryCount < maxRetries; retryCount++) {
                let cardElement = null;
                try {
                    log('debug', `Navigating back to Rewards dashboard to re-find element (Retry ${retryCount + 1}/${maxRetries})...`);
                    await this.page.goto(this.base_url);
                    await this.page.waitForTimeout(5000);
                    await this.dismissBanners();

                    const currentCards = await this.page.$$(
                        cardClickableXPaths.map(xpath => `xpath=${xpath}`).join(' | ')
                    );

                    const currentVisibleCards = [];
                    for (const card of currentCards) {
                        if (await card.isVisible()) {
                            currentVisibleCards.push(card);
                        }
                    }

                    // Try to find the specific element by its identifier
                    for (const currentCard of currentVisibleCards) {
                        const currentHref = await currentCard.getAttribute('href');
                        const currentDataBiId = await currentCard.getAttribute('data-bi-id');
                        const currentDataMAttr = await currentCard.getAttribute('data-m');

                        if ((taskInfo.href && currentHref === taskInfo.href) ||
                            (taskInfo.dataBiId && currentDataBiId === taskInfo.dataBiId) ||
                            (taskInfo.dataMAttr && currentDataMAttr === taskInfo.dataMAttr)) {
                            cardElement = currentCard;
                            log('debug', `Matched daily set card by identifier on retry ${retryCount + 1}.`);
                            break;
                        }
                    }

                    if (!cardElement && parseInt(originalIndex) < currentVisibleCards.length) {
                        cardElement = currentVisibleCards[parseInt(originalIndex)];
                        log('debug', `Matched daily set card by index ${originalIndex} on retry ${retryCount + 1}.`);
                    }

                    if (!cardElement) {
                        log('warning', `Could not re-find visible element for daily set task '${offerId}' on retry ${retryCount + 1}/${maxRetries}. Skipping processing for this task.`);
                        break;
                    }

                    const currentTaskStatus = await this.getDailySetItemStatus(cardElement);
                    if (currentTaskStatus === 'completed') {
                        log('info', `Daily set task '${offerId}' appears completed.`);
                        taskStatuses[originalIndex] = 'completed';
                        taskProcessedSuccessfully = true;
                        break;
                    } else if (currentTaskStatus === 'locked') {
                        log('info', `Daily set task '${offerId}' is locked. Skipping.`);
                        taskStatuses[originalIndex] = 'locked';
                        taskProcessedSuccessfully = true;
                        break;
                    } else if (currentTaskStatus === 'actionable') {
                        log('info', `Daily set task '${offerId}' is actionable. Attempting interaction (Retry ${retryCount + 1}/${maxRetries})...`);

                        await cardElement.scrollIntoViewIfNeeded();
                        await this.page.waitForTimeout(1000);
                        await cardElement.click();
                        log('info', `Clicked daily set task '${offerId}' successfully.`);

                        // Handle new tab or in-page activity
                        const pagePromises = [];
                        const newPagePromise = new Promise(resolve => this.browser.once('page', resolve));
                        pagePromises.push(newPagePromise);

                        const currentPages = this.browser.pages();
                        const initialPageCount = currentPages.length;

                        let newPage = null;
                        try {
                            newPage = await Promise.race([
                                newPagePromise,
                                this.page.waitForTimeout(5000) // Max wait for new page
                            ]);
                        } catch (e) {
                            log('debug', 'No new page opened quickly.');
                        }

                        if (newPage && newPage !== this.page) {
                            log('info', `Switched to new tab for daily set task '${offerId}'`);
                            await newPage.bringToFront();
                            await this.handleActivityPage(newPage);
                            log('info', 'Closing daily set activity tab and switching back.');
                            await newPage.close();
                            await this.page.bringToFront();
                            await this.page.waitForTimeout(3000);
                        } else {
                            log('warning', `Clicking daily set task '${offerId}' did not open a new tab or multiple windows already existed. Assuming in-page activity or simple link. Waiting...`);
                            await this.page.waitForTimeout(Math.random() * 5000 + 10000); // Wait 10-15 seconds
                            log('info', 'Finished waiting after in-page interaction attempt.');
                        }

                        taskStatuses[originalIndex] = 'attempted';
                        taskProcessedSuccessfully = true;
                        break;
                    }
                } catch (error) {
                    log('warning', `Error processing daily set task '${offerId}' on retry ${retryCount + 1}/${maxRetries}: ${error.message}. Retrying.`);
                    await this.page.waitForTimeout(2000);
                    if (retryCount === maxRetries - 1) {
                        log('error', `Max retries reached for daily set task '${offerId}' due to ${error.name}. Skipping task.`);
                        taskStatuses[originalIndex] = 'failed';
                    }
                }
            }

            if (!taskProcessedSuccessfully) {
                log('warning', `Daily set task '${offerId}' was not successfully processed after ${maxRetries} retries.`);
            } else {
                if (taskStatuses[originalIndex] === 'initial') {
                    taskStatuses[originalIndex] = 'attempted';
                }
            }
            log('info', `Finished processing logic for daily set task '${offerId}'. Final Status: ${taskStatuses[originalIndex]}.`);
        }

        log('info', 'Finished attempting daily set tasks.');
        for (const originalIndex in taskStatuses) {
            const offerId = taskIdentifiers.find(item => item.originalIndex === parseInt(originalIndex))?.id || 'N/A';
            log('info', `Daily Set Task (Original Index ${originalIndex}, ID: '${offerId}'): ${taskStatuses[originalIndex]}`);
        }
        return true;
    }

    async checkPointsBalance() {
        log('info', 'Checking points balance...');
        await this.page.goto(this.base_url);
        await this.dismissBanners();

        const pointsXPaths = [
            "//div[contains(@class, 'points-balance')]//span[contains(@class, 'points-value')]",
            "//div[contains(@aria-label, 'Microsoft Rewards points balance')]",
            "//div[contains(@class, 'balance-container')]//span[contains(@class, 'points')]",
            "//span[@id='id_rc']",
            "//div[contains(@class, 'points-card')]//span[contains(@class, 'points')]"
        ];

        for (const xpath of pointsXPaths) {
            try {
                const element = await this.page.waitForSelector(`xpath=${xpath}`, { state: 'visible', timeout: 5000 });
                let pointsText = await element.textContent();
                pointsText = pointsText.replace(/[^0-9]/g, ''); // Remove non-numeric characters
                const points = parseInt(pointsText, 10);
                if (!isNaN(points)) {
                    log('info', `Found points balance: ${points} using XPath: ${xpath}`);
                    return points;
                }
            } catch (error) {
                log('debug', `Could not find points using XPath ${xpath}: ${error.message}`);
            }
        }

        log('warning', 'Could not find points balance on the page.');
        return 0;
    }

    async handleActivityPage(page) {
        log('info', 'Attempting interactions on activity page...');
        try {
            await page.waitForLoadState('domcontentloaded', { timeout: 15000 });
            await page.waitForTimeout(Math.random() * 3000 + 3000); // Initial wait 3-6 seconds

            const interactiveElementsXPaths = [
                "//input[@type='radio']",
                "//div[contains(@class, 'option') or contains(@class, 'choice')]",
                "//button[contains(text(), 'Submit') or contains(text(), 'Next') or contains(text(), 'Play')]",
                "//a[contains(@class, 'btn') or contains(@class, 'button')]",
                "//button",
                "//a[contains(@href, '')]",
                "//div[@tabindex='0' and (contains(@role, 'button') or contains(@role, 'option'))]",
                "//span[contains(@class, 'answer') or contains(@class, 'option')]",
                "//label[contains(@class, 'option')]"
            ];

            const interactiveElements = await page.$$(
                interactiveElementsXPaths.map(xpath => `xpath=${xpath}`).join(' | ')
            );
            log('info', `Found ${interactiveElements.length} potential interactive elements on activity page.`);

            const interactableCandidates = [];
            for (const el of interactiveElements) {
                if (await el.isVisible() && await el.isEnabled()) {
                    interactableCandidates.push(el);
                }
            }
            log('debug', `Found ${interactableCandidates.length} interactable candidates.`);

            if (interactableCandidates.length > 0) {
                const randomElementsToClick = interactableCandidates.sort(() => 0.5 - Math.random()).slice(0, Math.min(interactableCandidates.length, 5));
                log('info', `Clicking ${randomElementsToClick.length} random interactive elements.`);
                for (let j = 0; j < randomElementsToClick.length; j++) {
                    const el = randomElementsToClick[j];
                    try {
                        await el.scrollIntoViewIfNeeded();
                        await page.waitForTimeout(500);
                        await el.click();
                        log('debug', `Clicked interactive element ${j + 1}/${randomElementsToClick.length}`);
                        await page.waitForTimeout(Math.random() * 2000 + 2000); // Wait 2-4 seconds
                    } catch (clickError) {
                        log('debug', `Could not click interactive element ${j + 1}: ${clickError.message}. Continuing.`);
                    }
                }
            } else {
                log('info', 'No visible and interactable common interactive elements found to click.');
            }

            log('info', 'Staying on activity page for sufficient time...');
            await page.waitForTimeout(Math.random() * 5000 + 5000); // Wait 5-10 seconds

        } catch (error) {
            log('warning', `Error during activity page interaction: ${error.message}`);
        }
    }

    async isOtherActivityComplete(cardElement) {
        try {
            const completeAttribute = await cardElement.getAttribute('data-complete');
            if (completeAttribute && completeAttribute.toLowerCase() === 'true') {
                log('debug', 'Activity complete via data-complete attribute.');
                return true;
            }

            const stateAttribute = await cardElement.getAttribute('data-state');
            if (stateAttribute && stateAttribute.toLowerCase() === 'completed') {
                log('debug', 'Activity complete via data-state attribute.');
                return true;
            }

            const checkmarkIcon = await cardElement.$("//span[contains(@class, 'mee-icon-SkypeCheck')] | //span[contains(@class, 'mee-icon-Checkmark')] | //span[contains(@class, 'mee-icon-Completed')] | //span[contains(@class, 'mee-icon-checkbox-fill')]");
            if (checkmarkIcon && await checkmarkIcon.isVisible()) {
                log('debug', 'Activity complete via checkmark icon.');
                return true;
            }

            const completedClass = await cardElement.$("//div[contains(@class, 'completed')] | .[contains(@class, 'completed')] | //mee-card[contains(@class, 'completed')]");
            if (completedClass && await completedClass.isVisible()) {
                log('debug', 'Activity complete via completed class.');
                return true;
            }

            return false;
        } catch (error) {
            log('warning', `Error checking other activity completion status: ${error.message}. Assuming not complete.`);
            return false;
        }
    }

    async getOtherActivityStatus(cardElement) {
        try {
            if (await this.isOtherActivityComplete(cardElement)) {
                return 'completed';
            }

            const ariaDisabled = await cardElement.getAttribute('aria-disabled');
            if (ariaDisabled && ariaDisabled.toLowerCase() === 'true') {
                log('debug', 'Other activity appears explicitly disabled (aria-disabled=true).');
                return 'locked';
            }

            const lockedCardClass = await cardElement.$("//div[contains(@class, 'locked-card')] | .[contains(@class, 'locked-card')] | //mee-card[contains(@class, 'locked-card')]");
            if (lockedCardClass && await lockedCardClass.isVisible()) {
                log('debug', 'Other activity appears locked via locked-card class.');
                return 'locked';
            }

            const lockIcon = await cardElement.$("//span[contains(@class, 'mee-icon-LockSolid')] | //span[contains(@class, 'mee-icon-LockFill')]");
            if (lockIcon && await lockIcon.isVisible()) {
                log('debug', 'Other activity appears locked via lock icon.');
                return 'locked';
            }

            return 'actionable';
        } catch (error) {
            log('warning', `Error checking other activity status: ${error.message}. Assuming 'actionable'.`);
            return 'actionable';
        }
    }

    async completeOtherActivities() {
        log('info', 'Starting other activities...');
        await this.page.goto(this.base_url);
        await this.dismissBanners();

        const otherActivitiesContainerXPath = "//*[@id='more-earning-section']";
        const cardClickableXPaths = [
            ".//div[contains(@class, 'rewards-card-container')]/a[contains(@class, 'ds-card-sec')]",
            ".//div[contains(@class, 'more-earning-card-item')]/a",
            ".//a[contains(@class, 'ds-card-sec')]",
            ".//div[contains(@class, 'rewards-card')]//mee-card",
            ".//div[contains(@class, 'promo-item')]//mee-card",
            ".//mee-card//a[contains(@href, '')]"
        ];

        let taskIdentifiers = [];

        try {
            const container = await this.page.waitForSelector(otherActivitiesContainerXPath, { state: 'visible', timeout: 10000 });
            log('info', 'Found other activities container.');

            const allCandidateCards = await container.$$(
                cardClickableXPaths.map(xpath => `xpath=${xpath}`).join(' | ')
            );

            const visibleCards = [];
            for (const card of allCandidateCards) {
                if (await card.isVisible()) {
                    visibleCards.push(card);
                }
            }
            log('info', `Identified ${visibleCards.length} visible other activity cards.`);

            for (let i = 0; i < visibleCards.length; i++) {
                const card = visibleCards[i];
                const href = await card.getAttribute('href');
                const dataBiId = await card.getAttribute('data-bi-id');
                const dataMAttr = await card.getAttribute('data-m');

                let id = href || dataBiId || dataMAttr || `Unknown_OtherActivity_${i}`;
                if (id.length > 50) id = id.substring(0, 50) + '...';

                taskIdentifiers.push({
                    originalIndex: i,
                    id: id,
                    href: href,
                    dataBiId: dataBiId,
                    dataMAttr: dataMAttr
                });
            }
            log('info', `Collected ${taskIdentifiers.length} other activity task identifiers.`);

        } catch (error) {
            log('error', `Error during initial scan for other activities: ${error.message}. Skipping.`);
            return false;
        }

        const taskStatuses = {};
        taskIdentifiers.forEach(info => taskStatuses[info.originalIndex] = 'initial');

        for (const originalIndex of Object.keys(taskStatuses).sort((a, b) => a - b)) {
            const taskInfo = taskIdentifiers.find(item => item.originalIndex === parseInt(originalIndex));
            if (!taskInfo) continue;

            const offerId = taskInfo.id;
            log('info', `Processing other activity task (original index ${originalIndex}): '${offerId}'...`);

            let taskProcessedSuccessfully = false;
            const maxRetries = 3;

            for (let retryCount = 0; retryCount < maxRetries; retryCount++) {
                let cardElement = null;
                try {
                    log('debug', `Navigating back to Rewards dashboard to re-find element (Retry ${retryCount + 1}/${maxRetries})...`);
                    await this.page.goto(this.base_url);
                    await this.page.waitForTimeout(5000);
                    await this.dismissBanners();

                    const currentCards = await this.page.$$(
                        cardClickableXPaths.map(xpath => `xpath=${xpath}`).join(' | ')
                    );

                    const currentVisibleCards = [];
                    for (const card of currentCards) {
                        if (await card.isVisible()) {
                            currentVisibleCards.push(card);
                        }
                    }

                    // Try to find the specific element by its identifier
                    for (const currentCard of currentVisibleCards) {
                        const currentHref = await currentCard.getAttribute('href');
                        const currentDataBiId = await currentCard.getAttribute('data-bi-id');
                        const currentDataMAttr = await currentCard.getAttribute('data-m');

                        if ((taskInfo.href && currentHref === taskInfo.href) ||
                            (taskInfo.dataBiId && currentDataBiId === taskInfo.dataBiId) ||
                            (taskInfo.dataMAttr && currentDataMAttr === taskInfo.dataMAttr)) {
                            cardElement = currentCard;
                            log('debug', `Matched other activity card by identifier on retry ${retryCount + 1}.`);
                            break;
                        }
                    }

                    if (!cardElement && parseInt(originalIndex) < currentVisibleCards.length) {
                        cardElement = currentVisibleCards[parseInt(originalIndex)];
                        log('debug', `Matched other activity card by index ${originalIndex} on retry ${retryCount + 1}.`);
                    }

                    if (!cardElement) {
                        log('warning', `Could not re-find visible element for other activity task '${offerId}' on retry ${retryCount + 1}/${maxRetries}. Skipping processing for this task.`);
                        break;
                    }

                    const currentTaskStatus = await this.getOtherActivityStatus(cardElement);
                    if (currentTaskStatus === 'completed') {
                        log('info', `Other activity task '${offerId}' appears completed.`);
                        taskStatuses[originalIndex] = 'completed';
                        taskProcessedSuccessfully = true;
                        break;
                    } else if (currentTaskStatus === 'locked') {
                        log('info', `Other activity task '${offerId}' is locked. Skipping.`);
                        taskStatuses[originalIndex] = 'locked';
                        taskProcessedSuccessfully = true;
                        break;
                    } else if (currentTaskStatus === 'actionable') {
                        log('info', `Other activity task '${offerId}' is actionable. Attempting interaction (Retry ${retryCount + 1}/${maxRetries})...`);

                        await cardElement.scrollIntoViewIfNeeded();
                        await this.page.waitForTimeout(1000);
                        await cardElement.click();
                        log('info', `Clicked other activity task '${offerId}' successfully.`);

                        // Handle new tab or in-page activity
                        const pagePromises = [];
                        const newPagePromise = new Promise(resolve => this.browser.once('page', resolve));
                        pagePromises.push(newPagePromise);

                        const currentPages = this.browser.pages();
                        const initialPageCount = currentPages.length;

                        let newPage = null;
                        try {
                            newPage = await Promise.race([
                                newPagePromise,
                                this.page.waitForTimeout(5000) // Max wait for new page
                            ]);
                        } catch (e) {
                            log('debug', 'No new page opened quickly.');
                        }

                        if (newPage && newPage !== this.page) {
                            log('info', `Switched to new tab for other activity task '${offerId}'`);
                            await newPage.bringToFront();
                            await this.handleActivityPage(newPage);
                            log('info', 'Closing other activity tab and switching back.');
                            await newPage.close();
                            await this.page.bringToFront();
                            await this.page.waitForTimeout(3000);
                        } else {
                            log('warning', `Clicking other activity task '${offerId}' did not open a new tab or multiple windows already existed. Assuming in-page activity or simple link. Waiting...`);
                            await this.page.waitForTimeout(Math.random() * 5000 + 10000); // Wait 10-15 seconds
                            log('info', 'Finished waiting after in-page interaction attempt.');
                        }

                        taskStatuses[originalIndex] = 'attempted';
                        taskProcessedSuccessfully = true;
                        break;
                    }
                } catch (error) {
                    log('warning', `Error processing other activity task '${offerId}' on retry ${retryCount + 1}/${maxRetries}: ${error.message}. Retrying.`);
                    await this.page.waitForTimeout(2000);
                    if (retryCount === maxRetries - 1) {
                        log('error', `Max retries reached for other activity task '${offerId}' due to ${error.name}. Skipping task.`);
                        taskStatuses[originalIndex] = 'failed';
                    }
                }
            }

            if (!taskProcessedSuccessfully) {
                log('warning', `Other activity task '${offerId}' was not successfully processed after ${maxRetries} retries.`);
            } else {
                if (taskStatuses[originalIndex] === 'initial') {
                    taskStatuses[originalIndex] = 'attempted';
                }
            }
            log('info', `Finished processing logic for other activity task '${offerId}'. Final Status: ${taskStatuses[originalIndex]}.`);
        }

        log('info', 'Finished attempting other activity tasks.');
        for (const originalIndex in taskStatuses) {
            const offerId = taskIdentifiers.find(item => item.originalIndex === parseInt(originalIndex))?.id || 'N/A';
            log('info', `Other Activity Task (Original Index ${originalIndex}, ID: '${offerId}'): ${taskStatuses[originalIndex]}`);
        }
        return true;
    }
}

(async () => {
    const bot = new MicrosoftRewardsBot();
    await bot.run();
})();







