2025-07-07 10:16:28,006 - INFO - Scheduler set up. <PERSON><PERSON> will run daily at 10:00 
2025-07-07 10:16:28,006 - INFO - Press Ctrl+C to exit the scheduler.
2025-07-07 10:16:28,007 - INFO - Running workflow immediately on script start...
2025-07-07 10:16:28,007 - INFO - Starting scheduled run of Microsoft Rewards Bot.
2025-07-07 10:16:28,007 - INFO - Using user data directory for persistent profile: C:\Users\<USER>\.ms_rewards_automation_profile
2025-07-07 10:16:28,007 - INFO - ----------------------------------------
2025-07-07 10:16:28,007 - INFO - Starting complete Microsoft Rewards workflow
2025-07-07 10:16:28,007 - INFO - Workflow started at: 2025-07-07 10:16:28
2025-07-07 10:16:28,007 - INFO - ----------------------------------------
2025-07-07 10:16:28,008 - INFO - Initializing Edge WebDriver via webdriver-manager...
2025-07-07 10:16:28,008 - INFO - ====== WebDriver manager ======
2025-07-07 10:16:29,179 - INFO - There is no [win64] edgedriver "latest" for browser edge "138.0.3351" in cache
2025-07-07 10:16:29,179 - INFO - Get LATEST edgedriver version for Edge 138.0.3351
2025-07-07 10:16:29,294 - INFO - About to download new driver from https://msedgedriver.azureedge.net/138.0.3351.65/edgedriver_win64.zip
2025-07-07 10:16:34,117 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\edgedriver\win64\138.0.3351.65]
2025-07-07 10:16:35,458 - INFO - Edge WebDriver initialized successfully.
2025-07-07 10:16:38,459 - INFO - Attempting login or verifying existing session.
2025-07-07 10:16:38,459 - INFO - Checking login status on rewards page...
2025-07-07 10:16:47,095 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:17:09,155 - INFO - Login status confirmed: Points element found and looks valid ('4,270').
2025-07-07 10:17:09,155 - INFO - Already logged in.
2025-07-07 10:17:09,156 - INFO - Checking points balance...
2025-07-07 10:17:09,158 - INFO - Navigating to rewards dashboard to check points.
2025-07-07 10:17:15,944 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:17:37,942 - INFO - Current points balance found: 4,270 (using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span)
2025-07-07 10:17:41,195 - INFO - Starting desktop searches (35 searches)...
2025-07-07 10:17:41,362 - INFO - Ensured desktop window size/maximization.
2025-07-07 10:17:46,627 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:18:08,516 - INFO - Found desktop search box using XPath: //textarea[@id='sb_form_q'].
2025-07-07 10:18:09,812 - INFO - Completed desktop search 1/35: 'types of robots 5878'
2025-07-07 10:18:25,970 - INFO - Completed desktop search 2/35: 'simple garden ideas 3456'
2025-07-07 10:18:40,015 - INFO - Completed desktop search 3/35: 'famous novels 7902'
2025-07-07 10:18:52,377 - INFO - Completed desktop search 4/35: 'types of birds 6299'
2025-07-07 10:19:08,327 - INFO - Completed desktop search 5/35: 'famous art exhibitions 3981'
2025-07-07 10:19:20,538 - INFO - Completed desktop search 6/35: 'artificial intelligence explained 9732'
2025-07-07 10:19:35,056 - INFO - Completed desktop search 7/35: 'mythological creatures 8053'
2025-07-07 10:19:46,358 - INFO - Completed desktop search 8/35: 'cheap vacation destinations 1057'
2025-07-07 10:20:00,845 - INFO - Completed desktop search 9/35: 'meaning of life 6324'
2025-07-07 10:20:13,163 - INFO - Completed desktop search 10/35: 'remote job opportunities 3659'
2025-07-07 10:20:28,868 - INFO - Completed desktop search 11/35: 'history of space travel 9429'
2025-07-07 10:20:44,133 - INFO - Completed desktop search 12/35: 'basketball history 1714'
2025-07-07 10:20:59,198 - INFO - Completed desktop search 13/35: 'how electricity works 4683'
2025-07-07 10:21:11,598 - INFO - Completed desktop search 14/35: 'learn a new language 6434'
2025-07-07 10:21:25,747 - INFO - Completed desktop search 15/35: 'popular movies to stream 5708'
2025-07-07 10:21:38,871 - INFO - Completed desktop search 16/35: 'landscape photography tips 1236'
2025-07-07 10:21:52,121 - INFO - Completed desktop search 17/35: 'astrophysics concepts 1141'
2025-07-07 10:22:05,909 - INFO - Completed desktop search 18/35: 'US political news 3713'
2025-07-07 10:22:20,033 - INFO - Completed desktop search 19/35: 'easy recipe ideas 5104'
2025-07-07 10:22:35,958 - INFO - Completed desktop search 20/35: 'global environmental issues 4013'
2025-07-07 10:22:47,891 - INFO - Completed desktop search 21/35: 'muscle building exercises 5979'
2025-07-07 10:23:03,543 - INFO - Completed desktop search 22/35: 'current fashion trends 5577'
2025-07-07 10:23:19,872 - INFO - Completed desktop search 23/35: 'famous scientists 2438'
2025-07-07 10:23:34,843 - INFO - Completed desktop search 24/35: 'stock market analysis 1176'
2025-07-07 10:23:48,279 - INFO - Completed desktop search 25/35: 'periodic table elements 5456'
2025-07-07 10:24:03,222 - INFO - Completed desktop search 26/35: 'beginner fitness workout 3303'
2025-07-07 10:24:17,889 - INFO - Completed desktop search 27/35: 'cybersecurity tips 5455'
2025-07-07 10:24:31,795 - INFO - Completed desktop search 28/35: 'highest waterfalls 7875'
2025-07-07 10:24:45,098 - INFO - Completed desktop search 29/35: 'major rivers of the world 6552'
2025-07-07 10:25:00,215 - INFO - Completed desktop search 30/35: 'swimming techniques 5913'
2025-07-07 10:25:11,577 - INFO - Completed desktop search 31/35: 'ocean currents 1676'
2025-07-07 10:25:24,954 - INFO - Completed desktop search 32/35: 'best coffee shops near me 9093'
2025-07-07 10:25:38,705 - INFO - Completed desktop search 33/35: 'political systems 7126'
2025-07-07 10:25:52,416 - INFO - Completed desktop search 34/35: 'types of martial arts 9380'
2025-07-07 10:26:07,569 - INFO - Completed desktop search 35/35: 'weather forecast today 4173'
2025-07-07 10:26:16,558 - INFO - Finished attempting desktop searches.
2025-07-07 10:26:19,781 - INFO - Starting mobile searches (25 searches)...
2025-07-07 10:26:19,788 - INFO - Set mobile user agent: Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36 EdgA/45.05.4.5058
2025-07-07 10:26:20,174 - INFO - Set window size to simulate mobile.
2025-07-07 10:26:25,393 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:27:02,328 - INFO - Found mobile search box using XPath: //input[@id='sb_form_q'].
2025-07-07 10:27:03,639 - INFO - Completed mobile search 1/25: 'types of robots 2799'
2025-07-07 10:27:25,589 - WARNING - Primary search box XPath (//input[@id='sb_form_q']) not found after search 2. Trying other XPaths.
2025-07-07 10:27:26,872 - INFO - Completed mobile search 2/25: 'simple garden ideas 7416'
2025-07-07 10:27:39,971 - INFO - Completed mobile search 3/25: 'famous novels 4737'
2025-07-07 10:27:53,871 - INFO - Completed mobile search 4/25: 'types of birds 3729'
2025-07-07 10:28:08,360 - INFO - Completed mobile search 5/25: 'famous art exhibitions 4438'
2025-07-07 10:28:25,125 - INFO - Completed mobile search 6/25: 'artificial intelligence explained 7698'
2025-07-07 10:28:37,791 - INFO - Completed mobile search 7/25: 'mythological creatures 6061'
2025-07-07 10:28:53,689 - INFO - Completed mobile search 8/25: 'cheap vacation destinations 6043'
2025-07-07 10:29:05,995 - INFO - Completed mobile search 9/25: 'meaning of life 2434'
2025-07-07 10:29:19,079 - INFO - Completed mobile search 10/25: 'remote job opportunities 3980'
2025-07-07 10:29:34,271 - INFO - Completed mobile search 11/25: 'history of space travel 2499'
2025-07-07 10:29:48,765 - INFO - Completed mobile search 12/25: 'basketball history 2493'
2025-07-07 10:29:59,650 - INFO - Completed mobile search 13/25: 'how electricity works 7339'
2025-07-07 10:30:14,469 - INFO - Completed mobile search 14/25: 'learn a new language 2909'
2025-07-07 10:30:28,598 - INFO - Completed mobile search 15/25: 'popular movies to stream 7560'
2025-07-07 10:30:43,446 - INFO - Completed mobile search 16/25: 'landscape photography tips 9394'
2025-07-07 10:30:56,984 - INFO - Completed mobile search 17/25: 'astrophysics concepts 3919'
2025-07-07 10:31:11,077 - INFO - Completed mobile search 18/25: 'US political news 1348'
2025-07-07 10:31:23,365 - INFO - Completed mobile search 19/25: 'easy recipe ideas 2384'
2025-07-07 10:31:35,455 - INFO - Completed mobile search 20/25: 'global environmental issues 1003'
2025-07-07 10:31:47,231 - INFO - Completed mobile search 21/25: 'muscle building exercises 7283'
2025-07-07 10:32:02,126 - INFO - Completed mobile search 22/25: 'current fashion trends 8063'
2025-07-07 10:32:18,386 - INFO - Completed mobile search 23/25: 'famous scientists 2677'
2025-07-07 10:32:33,669 - INFO - Completed mobile search 24/25: 'stock market analysis 2195'
2025-07-07 10:32:47,866 - INFO - Completed mobile search 25/25: 'periodic table elements 9910'
2025-07-07 10:33:01,200 - INFO - Finished attempting mobile searches.
2025-07-07 10:33:01,230 - INFO - Reset user agent to default desktop and maximized window.
2025-07-07 10:33:03,234 - INFO - Starting daily set tasks...
2025-07-07 10:33:03,240 - INFO - Navigating to rewards dashboard for daily set.
2025-07-07 10:33:10,260 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:33:32,242 - INFO - Attempting to find daily set container with XPath: //*[@id='daily-sets']
2025-07-07 10:33:32,257 - INFO - Found daily set container.
2025-07-07 10:33:32,283 - INFO - Identified 3 visible daily set cards within the container.
2025-07-07 10:33:32,301 - INFO - Collected 3 daily set task identifiers.
2025-07-07 10:33:32,301 - INFO - Processing daily set task (original index 0): 'ID_Found_0'...
2025-07-07 10:33:40,366 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:34:07,443 - WARNING - Timeout waiting for element/page on retry 1/3 for daily set task 'ID_Found_0'. Retrying.
2025-07-07 10:34:17,552 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:34:44,563 - WARNING - Timeout waiting for element/page on retry 2/3 for daily set task 'ID_Found_0'. Retrying.
2025-07-07 10:34:54,724 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:35:21,953 - WARNING - Timeout waiting for element/page on retry 3/3 for daily set task 'ID_Found_0'. Retrying.
2025-07-07 10:35:23,956 - ERROR - Max retries reached for daily set task 'ID_Found_0' due to Timeout. Skipping task.
2025-07-07 10:35:23,956 - WARNING - Daily set task 'ID_Found_0' was not successfully processed after 3 retries.
2025-07-07 10:35:23,957 - INFO - Finished processing logic for daily set task 'ID_Found_0'. Final Status: failed.
2025-07-07 10:35:23,958 - INFO - Processing daily set task (original index 1): 'ID_Found_1'...
2025-07-07 10:35:32,052 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:35:59,350 - WARNING - Timeout waiting for element/page on retry 1/3 for daily set task 'ID_Found_1'. Retrying.
2025-07-07 10:36:09,450 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:36:36,660 - WARNING - Timeout waiting for element/page on retry 2/3 for daily set task 'ID_Found_1'. Retrying.
2025-07-07 10:36:46,686 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:37:13,842 - WARNING - Timeout waiting for element/page on retry 3/3 for daily set task 'ID_Found_1'. Retrying.
2025-07-07 10:37:15,846 - ERROR - Max retries reached for daily set task 'ID_Found_1' due to Timeout. Skipping task.
2025-07-07 10:37:15,846 - WARNING - Daily set task 'ID_Found_1' was not successfully processed after 3 retries.
2025-07-07 10:37:15,848 - INFO - Finished processing logic for daily set task 'ID_Found_1'. Final Status: failed.
2025-07-07 10:37:15,849 - INFO - Processing daily set task (original index 2): 'ID_Found_2'...
2025-07-07 10:37:23,901 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:37:51,065 - WARNING - Timeout waiting for element/page on retry 1/3 for daily set task 'ID_Found_2'. Retrying.
2025-07-07 10:38:01,330 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:38:28,505 - WARNING - Timeout waiting for element/page on retry 2/3 for daily set task 'ID_Found_2'. Retrying.
2025-07-07 10:38:38,647 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:39:05,778 - WARNING - Timeout waiting for element/page on retry 3/3 for daily set task 'ID_Found_2'. Retrying.
2025-07-07 10:39:07,790 - ERROR - Max retries reached for daily set task 'ID_Found_2' due to Timeout. Skipping task.
2025-07-07 10:39:07,790 - WARNING - Daily set task 'ID_Found_2' was not successfully processed after 3 retries.
2025-07-07 10:39:07,790 - INFO - Finished processing logic for daily set task 'ID_Found_2'. Final Status: failed.
2025-07-07 10:39:07,791 - INFO - Finished attempting daily set tasks.
2025-07-07 10:39:07,791 - INFO - Daily Set Task (Original Index 0, ID: 'ID_Found_0'): failed
2025-07-07 10:39:07,791 - INFO - Daily Set Task (Original Index 1, ID: 'ID_Found_1'): failed
2025-07-07 10:39:07,791 - INFO - Daily Set Task (Original Index 2, ID: 'ID_Found_2'): failed
2025-07-07 10:39:07,791 - INFO - Looking for other point activities...
2025-07-07 10:39:07,794 - INFO - Navigating to rewards dashboard for other activities.
2025-07-07 10:39:13,882 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:39:35,804 - INFO - Found other activities container.
2025-07-07 10:39:35,815 - INFO - Identified 2 visible other activity cards within container.
2025-07-07 10:39:35,826 - INFO - Collected 2 other activity task identifiers.
2025-07-07 10:39:35,826 - INFO - Processing other activity task (original index 0): 'ID_Found_0'...
2025-07-07 10:39:43,916 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:40:11,087 - WARNING - Timeout waiting for element/page on retry 1/3 for other activity task 'ID_Found_0'. Retrying.
2025-07-07 10:40:21,339 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:40:48,584 - WARNING - Timeout waiting for element/page on retry 2/3 for other activity task 'ID_Found_0'. Retrying.
2025-07-07 10:40:58,614 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:41:25,815 - WARNING - Timeout waiting for element/page on retry 3/3 for other activity task 'ID_Found_0'. Retrying.
2025-07-07 10:41:27,823 - ERROR - Max retries reached for other activity task 'ID_Found_0' due to Timeout. Skipping task.
2025-07-07 10:41:27,823 - WARNING - Other activity task 'ID_Found_0' was not successfully processed after 3 retries.
2025-07-07 10:41:27,825 - INFO - Finished processing logic for other activity task 'ID_Found_0'. Final Status: failed.
2025-07-07 10:41:27,825 - INFO - Processing other activity task (original index 1): 'ID_Found_1'...
2025-07-07 10:41:35,912 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:42:03,229 - WARNING - Timeout waiting for element/page on retry 1/3 for other activity task 'ID_Found_1'. Retrying.
2025-07-07 10:42:13,466 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:42:40,724 - WARNING - Timeout waiting for element/page on retry 2/3 for other activity task 'ID_Found_1'. Retrying.
2025-07-07 10:42:50,931 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:43:18,128 - WARNING - Timeout waiting for element/page on retry 3/3 for other activity task 'ID_Found_1'. Retrying.
2025-07-07 10:43:20,139 - ERROR - Max retries reached for other activity task 'ID_Found_1' due to Timeout. Skipping task.
2025-07-07 10:43:20,139 - WARNING - Other activity task 'ID_Found_1' was not successfully processed after 3 retries.
2025-07-07 10:43:20,140 - INFO - Finished processing logic for other activity task 'ID_Found_1'. Final Status: failed.
2025-07-07 10:43:20,140 - INFO - Finished attempting other activities.
2025-07-07 10:43:20,141 - INFO - Other Activity Task (Original Index 0, ID: 'ID_Found_0'): failed
2025-07-07 10:43:20,141 - INFO - Other Activity Task (Original Index 1, ID: 'ID_Found_1'): failed
2025-07-07 10:43:20,141 - INFO - Checking points balance...
2025-07-07 10:43:20,149 - INFO - Navigating to rewards dashboard to check points.
2025-07-07 10:43:26,214 - INFO - Attempting to dismiss potential banners/popups.
2025-07-07 10:43:48,167 - INFO - Current points balance found: 4,282 (using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span)
2025-07-07 10:43:48,167 - INFO - Workflow completed. Points: 4,270 -> 4,282
2025-07-07 10:43:48,167 - INFO - Quitting Edge WebDriver.
2025-07-07 10:43:50,429 - INFO - WebDriver quit successfully.
2025-07-07 10:43:50,430 - INFO - ----------------------------------------
2025-07-07 10:43:50,430 - INFO - Workflow process finished. Browser window is closed.
2025-07-07 10:43:50,431 - INFO - ----------------------------------------
2025-07-07 10:43:50,431 - INFO - Scheduled run completed successfully.
2025-07-07 10:43:50,432 - INFO - ----------------------------------------
2025-07-07 10:43:50,432 - INFO - Scheduled run process finished. Waiting for next run.
2025-07-07 10:43:50,432 - INFO - ----------------------------------------
2025-07-07 10:43:50,433 - INFO - Initial run completed. Entering scheduling loop.
2025-07-13 17:54:44,699 - INFO - Scheduler set up. Bot will run daily at 10:00 
2025-07-13 17:54:44,702 - INFO - Press Ctrl+C to exit the scheduler.
2025-07-13 17:54:44,702 - INFO - Running workflow immediately on script start...
2025-07-13 17:54:44,702 - INFO - Starting scheduled run of Microsoft Rewards Bot.
2025-07-13 17:54:44,702 - INFO - Using user data directory for persistent profile: C:\Users\<USER>\.ms_rewards_automation_profile
2025-07-13 17:54:44,702 - INFO - ----------------------------------------
2025-07-13 17:54:44,702 - INFO - Starting complete Microsoft Rewards workflow
2025-07-13 17:54:44,702 - INFO - Workflow started at: 2025-07-13 17:54:44
2025-07-13 17:54:44,702 - INFO - ----------------------------------------
2025-07-13 17:54:44,702 - INFO - Initializing Edge WebDriver via webdriver-manager...
2025-07-13 17:54:44,702 - INFO - ====== WebDriver manager ======
2025-07-13 17:54:46,046 - INFO - Get LATEST edgedriver version for Edge 138.0.3351
2025-07-13 17:54:46,310 - INFO - About to download new driver from https://msedgedriver.azureedge.net/138.0.3351.83/edgedriver_win64.zip
2025-07-13 17:54:51,575 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\edgedriver\win64\138.0.3351.83]
2025-07-13 17:54:52,675 - INFO - Edge WebDriver initialized successfully.
2025-07-13 17:54:55,677 - INFO - Attempting login or verifying existing session.
2025-07-13 17:54:55,677 - INFO - Checking login status on rewards page...
2025-07-13 17:55:03,425 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 17:55:25,619 - INFO - Login status confirmed: Points element found and looks valid ('381').
2025-07-13 17:55:25,619 - INFO - Already logged in.
2025-07-13 17:55:25,620 - INFO - Checking points balance...
2025-07-13 17:55:25,622 - INFO - Navigating to rewards dashboard to check points.
2025-07-13 17:55:32,653 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 17:55:54,585 - INFO - Current points balance found: 381 (using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span)
2025-07-13 17:56:00,109 - INFO - Starting desktop searches (35 searches)...
2025-07-13 17:56:00,172 - INFO - Ensured desktop window size/maximization.
2025-07-13 17:56:05,363 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 17:56:27,443 - INFO - Found desktop search box using XPath: //textarea[@id='sb_form_q'].
2025-07-13 17:56:29,285 - INFO - Completed desktop search 1/35: 'muscle building exercises 5907'
2025-07-13 17:56:41,145 - INFO - Completed desktop search 2/35: 'healthy breakfast ideas 4787'
2025-07-13 17:56:58,098 - INFO - Completed desktop search 3/35: 'learn a new language 7465'
2025-07-13 17:57:14,155 - INFO - Completed desktop search 4/35: 'modern architecture 9710'
2025-07-13 17:57:26,055 - INFO - Completed desktop search 5/35: 'different dog breeds 3305'
2025-07-13 17:57:39,375 - INFO - Completed desktop search 6/35: 'world largest deserts 9394'
2025-07-13 17:57:53,147 - INFO - Completed desktop search 7/35: 'classical music composers 5739'
2025-07-13 17:58:06,941 - INFO - Completed desktop search 8/35: 'best coffee shops near me 8999'
2025-07-13 17:58:19,240 - INFO - Completed desktop search 9/35: 'weather forecast today 1486'
2025-07-13 17:58:32,730 - INFO - Completed desktop search 10/35: 'tennis grand slams 3085'
2025-07-13 17:58:47,711 - INFO - Completed desktop search 11/35: 'famous equations 6388'
2025-07-13 17:58:59,939 - INFO - Completed desktop search 12/35: 'marathon training plan 6824'
2025-07-13 17:59:11,300 - INFO - Completed desktop search 13/35: 'new music releases 2024 3366'
2025-07-13 17:59:25,168 - INFO - Completed desktop search 14/35: 'ocean currents 6141'
2025-07-13 17:59:38,665 - INFO - Completed desktop search 15/35: 'basketball history 3511'
2025-07-13 17:59:50,785 - INFO - Completed desktop search 16/35: 'mythological creatures 8493'
2025-07-13 18:00:04,231 - INFO - Completed desktop search 17/35: 'cybersecurity tips 3982'
2025-07-13 18:00:21,023 - INFO - Completed desktop search 18/35: 'cheap vacation destinations 1132'
2025-07-13 18:00:37,690 - INFO - Completed desktop search 19/35: 'periodic table elements 3478'
2025-07-13 18:00:51,606 - INFO - Completed desktop search 20/35: 'history of the internet 3657'
2025-07-13 18:01:06,169 - INFO - Completed desktop search 21/35: 'famous battles in history 3873'
2025-07-13 18:01:22,243 - INFO - Completed desktop search 22/35: 'current fashion trends 8962'
2025-07-13 18:01:37,223 - INFO - Completed desktop search 23/35: 'meaning of life 3143'
2025-07-13 18:01:49,746 - INFO - Completed desktop search 24/35: 'renaissance art 2662'
2025-07-13 18:02:03,327 - INFO - Completed desktop search 25/35: 'famous historical figures 9190'
2025-07-13 18:02:14,759 - INFO - Completed desktop search 26/35: 'latest news headlines 6697'
2025-07-13 18:02:30,837 - INFO - Completed desktop search 27/35: 'photography composition rules 1678'
2025-07-13 18:02:44,250 - INFO - Completed desktop search 28/35: 'football rules 1907'
2025-07-13 18:02:58,819 - INFO - Completed desktop search 29/35: 'astrophysics concepts 8384'
2025-07-13 18:03:12,503 - INFO - Completed desktop search 30/35: 'video game reviews 9867'
2025-07-13 18:03:25,752 - INFO - Completed desktop search 31/35: 'popular movies to stream 6195'
2025-07-13 18:03:36,885 - INFO - Completed desktop search 32/35: 'basic car maintenance checks 6260'
2025-07-13 18:03:50,157 - INFO - Completed desktop search 33/35: 'famous explorers 9586'
2025-07-13 18:04:04,604 - INFO - Completed desktop search 34/35: 'famous philosophers 8001'
2025-07-13 18:04:18,401 - INFO - Completed desktop search 35/35: 'types of paradoxes 8028'
2025-07-13 18:04:33,090 - INFO - Finished attempting desktop searches.
2025-07-13 18:04:36,290 - INFO - Starting mobile searches (25 searches)...
2025-07-13 18:04:36,346 - INFO - Set mobile user agent: Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36 EdgA/45.05.4.5058
2025-07-13 18:04:36,730 - INFO - Set window size to simulate mobile.
2025-07-13 18:04:41,976 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:05:19,141 - INFO - Found mobile search box using XPath: //input[@id='sb_form_q'].
2025-07-13 18:05:20,437 - INFO - Completed mobile search 1/25: 'muscle building exercises 1058'
2025-07-13 18:05:44,009 - WARNING - Primary search box XPath (//input[@id='sb_form_q']) not found after search 2. Trying other XPaths.
2025-07-13 18:05:45,270 - INFO - Completed mobile search 2/25: 'healthy breakfast ideas 2290'
2025-07-13 18:06:02,078 - INFO - Completed mobile search 3/25: 'learn a new language 3197'
2025-07-13 18:06:14,316 - INFO - Completed mobile search 4/25: 'modern architecture 1009'
2025-07-13 18:06:27,038 - INFO - Completed mobile search 5/25: 'different dog breeds 4586'
2025-07-13 18:06:41,812 - INFO - Completed mobile search 6/25: 'world largest deserts 7570'
2025-07-13 18:06:54,984 - INFO - Completed mobile search 7/25: 'classical music composers 6108'
2025-07-13 18:07:11,061 - INFO - Completed mobile search 8/25: 'best coffee shops near me 1799'
2025-07-13 18:07:23,901 - INFO - Completed mobile search 9/25: 'weather forecast today 1059'
2025-07-13 18:07:37,599 - INFO - Completed mobile search 10/25: 'tennis grand slams 6590'
2025-07-13 18:07:54,524 - INFO - Completed mobile search 11/25: 'famous equations 9397'
2025-07-13 18:08:07,516 - INFO - Completed mobile search 12/25: 'marathon training plan 4735'
2025-07-13 18:08:22,140 - INFO - Completed mobile search 13/25: 'new music releases 2024 5960'
2025-07-13 18:08:36,384 - INFO - Completed mobile search 14/25: 'ocean currents 6389'
2025-07-13 18:08:47,793 - INFO - Completed mobile search 15/25: 'basketball history 3707'
2025-07-13 18:09:02,211 - INFO - Completed mobile search 16/25: 'mythological creatures 7231'
2025-07-13 18:09:15,510 - INFO - Completed mobile search 17/25: 'cybersecurity tips 6052'
2025-07-13 18:09:26,910 - INFO - Completed mobile search 18/25: 'cheap vacation destinations 1360'
2025-07-13 18:09:39,646 - INFO - Completed mobile search 19/25: 'periodic table elements 2923'
2025-07-13 18:09:55,713 - INFO - Completed mobile search 20/25: 'history of the internet 9293'
2025-07-13 18:10:07,619 - INFO - Completed mobile search 21/25: 'famous battles in history 8659'
2025-07-13 18:10:21,380 - INFO - Completed mobile search 22/25: 'current fashion trends 5558'
2025-07-13 18:10:36,117 - INFO - Completed mobile search 23/25: 'meaning of life 5786'
2025-07-13 18:10:46,514 - INFO - Completed mobile search 24/25: 'renaissance art 8588'
2025-07-13 18:10:57,787 - INFO - Completed mobile search 25/25: 'famous historical figures 4701'
2025-07-13 18:11:09,188 - INFO - Finished attempting mobile searches.
2025-07-13 18:11:09,237 - INFO - Reset user agent to default desktop and maximized window.
2025-07-13 18:11:11,251 - INFO - Starting daily set tasks...
2025-07-13 18:11:11,255 - INFO - Navigating to rewards dashboard for daily set.
2025-07-13 18:11:18,365 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:11:40,436 - INFO - Attempting to find daily set container with XPath: //*[@id='daily-sets']
2025-07-13 18:11:40,446 - INFO - Found daily set container.
2025-07-13 18:11:40,479 - INFO - Identified 3 visible daily set cards within the container.
2025-07-13 18:11:40,501 - INFO - Collected 3 daily set task identifiers.
2025-07-13 18:11:40,501 - INFO - Processing daily set task (original index 0): 'ID_Found_0'...
2025-07-13 18:11:48,809 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:12:15,799 - WARNING - Timeout waiting for element/page on retry 1/3 for daily set task 'ID_Found_0'. Retrying.
2025-07-13 18:12:26,186 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:12:53,163 - WARNING - Timeout waiting for element/page on retry 2/3 for daily set task 'ID_Found_0'. Retrying.
2025-07-13 18:13:03,545 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:13:30,852 - WARNING - Timeout waiting for element/page on retry 3/3 for daily set task 'ID_Found_0'. Retrying.
2025-07-13 18:13:32,859 - ERROR - Max retries reached for daily set task 'ID_Found_0' due to Timeout. Skipping task.
2025-07-13 18:13:32,859 - WARNING - Daily set task 'ID_Found_0' was not successfully processed after 3 retries.
2025-07-13 18:13:32,863 - INFO - Finished processing logic for daily set task 'ID_Found_0'. Final Status: failed.
2025-07-13 18:13:32,869 - INFO - Processing daily set task (original index 1): 'ID_Found_1'...
2025-07-13 18:13:41,290 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:14:08,552 - WARNING - Timeout waiting for element/page on retry 1/3 for daily set task 'ID_Found_1'. Retrying.
2025-07-13 18:14:19,007 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:14:46,259 - WARNING - Timeout waiting for element/page on retry 2/3 for daily set task 'ID_Found_1'. Retrying.
2025-07-13 18:14:56,549 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:15:23,686 - WARNING - Timeout waiting for element/page on retry 3/3 for daily set task 'ID_Found_1'. Retrying.
2025-07-13 18:15:25,702 - ERROR - Max retries reached for daily set task 'ID_Found_1' due to Timeout. Skipping task.
2025-07-13 18:15:25,702 - WARNING - Daily set task 'ID_Found_1' was not successfully processed after 3 retries.
2025-07-13 18:15:25,703 - INFO - Finished processing logic for daily set task 'ID_Found_1'. Final Status: failed.
2025-07-13 18:15:25,703 - INFO - Processing daily set task (original index 2): 'ID_Found_2'...
2025-07-13 18:15:34,162 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:16:01,223 - WARNING - Timeout waiting for element/page on retry 1/3 for daily set task 'ID_Found_2'. Retrying.
2025-07-13 18:16:12,317 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:16:39,657 - WARNING - Timeout waiting for element/page on retry 2/3 for daily set task 'ID_Found_2'. Retrying.
2025-07-13 18:16:50,341 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:17:18,951 - WARNING - Timeout waiting for element/page on retry 3/3 for daily set task 'ID_Found_2'. Retrying.
2025-07-13 18:17:20,952 - ERROR - Max retries reached for daily set task 'ID_Found_2' due to Timeout. Skipping task.
2025-07-13 18:17:20,952 - WARNING - Daily set task 'ID_Found_2' was not successfully processed after 3 retries.
2025-07-13 18:17:20,953 - INFO - Finished processing logic for daily set task 'ID_Found_2'. Final Status: failed.
2025-07-13 18:17:20,953 - INFO - Finished attempting daily set tasks.
2025-07-13 18:17:20,954 - INFO - Daily Set Task (Original Index 0, ID: 'ID_Found_0'): failed
2025-07-13 18:17:20,954 - INFO - Daily Set Task (Original Index 1, ID: 'ID_Found_1'): failed
2025-07-13 18:17:20,955 - INFO - Daily Set Task (Original Index 2, ID: 'ID_Found_2'): failed
2025-07-13 18:17:20,955 - INFO - Looking for other point activities...
2025-07-13 18:17:20,965 - INFO - Navigating to rewards dashboard for other activities.
2025-07-13 18:17:27,427 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:17:49,436 - INFO - Found other activities container.
2025-07-13 18:17:49,453 - INFO - Identified 3 visible other activity cards within container.
2025-07-13 18:17:49,468 - INFO - Collected 3 other activity task identifiers.
2025-07-13 18:17:49,469 - INFO - Processing other activity task (original index 0): 'ID_Found_0'...
2025-07-13 18:17:57,908 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:18:28,003 - WARNING - Timeout waiting for element/page on retry 1/3 for other activity task 'ID_Found_0'. Retrying.
2025-07-13 18:18:38,769 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:19:05,948 - WARNING - Timeout waiting for element/page on retry 2/3 for other activity task 'ID_Found_0'. Retrying.
2025-07-13 18:19:16,309 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:19:43,389 - WARNING - Timeout waiting for element/page on retry 3/3 for other activity task 'ID_Found_0'. Retrying.
2025-07-13 18:19:45,405 - ERROR - Max retries reached for other activity task 'ID_Found_0' due to Timeout. Skipping task.
2025-07-13 18:19:45,405 - WARNING - Other activity task 'ID_Found_0' was not successfully processed after 3 retries.
2025-07-13 18:19:45,406 - INFO - Finished processing logic for other activity task 'ID_Found_0'. Final Status: failed.
2025-07-13 18:19:45,406 - INFO - Processing other activity task (original index 1): 'ID_Found_1'...
2025-07-13 18:19:53,900 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:20:21,085 - WARNING - Timeout waiting for element/page on retry 1/3 for other activity task 'ID_Found_1'. Retrying.
2025-07-13 18:20:31,369 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:20:58,695 - WARNING - Timeout waiting for element/page on retry 2/3 for other activity task 'ID_Found_1'. Retrying.
2025-07-13 18:21:09,177 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:21:39,682 - WARNING - Timeout waiting for element/page on retry 3/3 for other activity task 'ID_Found_1'. Retrying.
2025-07-13 18:21:41,696 - ERROR - Max retries reached for other activity task 'ID_Found_1' due to Timeout. Skipping task.
2025-07-13 18:21:41,697 - WARNING - Other activity task 'ID_Found_1' was not successfully processed after 3 retries.
2025-07-13 18:21:41,698 - INFO - Finished processing logic for other activity task 'ID_Found_1'. Final Status: failed.
2025-07-13 18:21:41,698 - INFO - Processing other activity task (original index 2): 'ID_Found_2'...
2025-07-13 18:21:50,213 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:22:17,640 - WARNING - Timeout waiting for element/page on retry 1/3 for other activity task 'ID_Found_2'. Retrying.
2025-07-13 18:22:28,182 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:22:55,620 - WARNING - Timeout waiting for element/page on retry 2/3 for other activity task 'ID_Found_2'. Retrying.
2025-07-13 18:23:06,338 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:23:33,655 - WARNING - Timeout waiting for element/page on retry 3/3 for other activity task 'ID_Found_2'. Retrying.
2025-07-13 18:23:35,661 - ERROR - Max retries reached for other activity task 'ID_Found_2' due to Timeout. Skipping task.
2025-07-13 18:23:35,661 - WARNING - Other activity task 'ID_Found_2' was not successfully processed after 3 retries.
2025-07-13 18:23:35,661 - INFO - Finished processing logic for other activity task 'ID_Found_2'. Final Status: failed.
2025-07-13 18:23:35,661 - INFO - Finished attempting other activities.
2025-07-13 18:23:35,661 - INFO - Other Activity Task (Original Index 0, ID: 'ID_Found_0'): failed
2025-07-13 18:23:35,662 - INFO - Other Activity Task (Original Index 1, ID: 'ID_Found_1'): failed
2025-07-13 18:23:35,662 - INFO - Other Activity Task (Original Index 2, ID: 'ID_Found_2'): failed
2025-07-13 18:23:35,662 - INFO - Checking points balance...
2025-07-13 18:23:35,666 - INFO - Navigating to rewards dashboard to check points.
2025-07-13 18:23:41,959 - INFO - Attempting to dismiss potential banners/popups.
2025-07-13 18:24:03,888 - INFO - Current points balance found: 224 (using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span)
2025-07-13 18:24:03,889 - INFO - Workflow completed. Points: 381 -> 224
2025-07-13 18:24:03,889 - INFO - Quitting Edge WebDriver.
2025-07-13 18:24:06,213 - INFO - WebDriver quit successfully.
2025-07-13 18:24:06,213 - INFO - ----------------------------------------
2025-07-13 18:24:06,214 - INFO - Workflow process finished. Browser window is closed.
2025-07-13 18:24:06,214 - INFO - ----------------------------------------
2025-07-13 18:24:06,214 - INFO - Scheduled run completed successfully.
2025-07-13 18:24:06,215 - INFO - ----------------------------------------
2025-07-13 18:24:06,215 - INFO - Scheduled run process finished. Waiting for next run.
2025-07-13 18:24:06,215 - INFO - ----------------------------------------
2025-07-13 18:24:06,215 - INFO - Initial run completed. Entering scheduling loop.
[2025-07-14T07:21:01.241Z] [INFO] Launching browser...
[2025-07-14T07:21:01.243Z] [ERROR] Failed to launch browser: userDataDir option is not supported in `browserType.launch`. Use `browserType.launchPersistentContext` instead
[2025-07-14T07:21:01.243Z] [ERROR] Automation workflow failed: userDataDir option is not supported in `browserType.launch`. Use `browserType.launchPersistentContext` instead
[2025-07-14T07:22:28.502Z] [INFO] Launching browser...
[2025-07-14T07:22:28.816Z] [INFO] Browser launched successfully.
[2025-07-14T07:22:28.816Z] [INFO] Attempting to log in...
[2025-07-14T07:22:34.260Z] [INFO] Attempting to dismiss banners...
[2025-07-14T07:22:39.272Z] [DEBUG] No banner found for XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@aria-label, \'Tutup\') or contains(@aria-label, \'Close\') or contains(text(), \'Tidak, terima kasih\') or contains(text(), \'No thanks\')]') to be visible[22m
[2m    - waiting for navigation to finish...[22m
[2m    - navigated to "https://rewards.bing.com/"[22m
[2m    3 × locator resolved to hidden <button tabindex="0" role="button" aria-label="Close" data-modal-close-button="" class="c-glyph glyph-cancel" ng-show="$ctrl.hasCloseButton" mee-progress-hidden="modalOperation" ng-attr-aria-label="{{$ctrl.meeAriaCloseButtonLabel}}"></button>[22m

[2025-07-14T07:22:44.279Z] [DEBUG] No banner found for XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//span[contains(@class, \'cancel\') or contains(@class, \'close\')]') to be visible[22m
[2m    15 × locator resolved to 2 elements. Proceeding with the first one: <span tabindex="0" role="button" aria-label="Close" class="autoredeem-close-cross" id="autoredeemWarningModalCloseCross" ng-click="$ctrl.hideWarningModal($event)" data-bi-id="rx-nav-autoredeem-banner-warning-close-cross">×</span>[22m

[2025-07-14T07:22:49.287Z] [DEBUG] No banner found for XPath: //a[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//a[contains(@aria-label, \'Tutup\') or contains(@aria-label, \'Close\')]') to be visible[22m

[2025-07-14T07:22:54.297Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'declineButton') or contains(@id, 'closeButton')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@id, \'declineButton\') or contains(@id, \'closeButton\')]') to be visible[22m

[2025-07-14T07:22:59.299Z] [DEBUG] No banner found for XPath: //div[contains(@class, 'lightbox-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'lightbox-close\')]') to be visible[22m

[2025-07-14T07:23:04.301Z] [DEBUG] No banner found for XPath: //div[contains(@class, 'common-banner-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'common-banner-close\')]') to be visible[22m

[2025-07-14T07:23:09.307Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'notification-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'notification-close\')]') to be visible[22m

[2025-07-14T07:23:14.316Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'close-button')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'close-button\')]') to be visible[22m

[2025-07-14T07:23:19.330Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'glif-dismiss')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'glif-dismiss\')]') to be visible[22m

[2025-07-14T07:23:24.348Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'me-Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'me-Close\')]') to be visible[22m

[2025-07-14T07:23:29.352Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'ub-emb-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'ub-emb-close\')]') to be visible[22m

[2025-07-14T07:23:34.358Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'ot-close-icon')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'ot-close-icon\')]') to be visible[22m

[2025-07-14T07:23:39.365Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'c-action-trigger') and contains(@aria-label, 'Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'c-action-trigger\') and contains(@aria-label, \'Close\')]') to be visible[22m

[2025-07-14T07:23:44.368Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'f-button') and contains(text(), 'No thanks')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'f-button\') and contains(text(), \'No thanks\')]') to be visible[22m

[2025-07-14T07:23:49.378Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'cookie-consent-button') and contains(text(), 'Accept')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'cookie-consent-button\') and contains(text(), \'Accept\')]') to be visible[22m

[2025-07-14T07:23:54.387Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'cookie-button') and contains(text(), 'Accept')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'cookie-button\') and contains(text(), \'Accept\')]') to be visible[22m

[2025-07-14T07:23:59.401Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'onetrust-accept-btn-handler')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@id, \'onetrust-accept-btn-handler\')]') to be visible[22m

[2025-07-14T07:24:04.412Z] [DEBUG] No banner found for XPath: //a[contains(@id, 'RSTC_Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//a[contains(@id, \'RSTC_Close\')]') to be visible[22m

[2025-07-14T07:24:09.420Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'RSTC_Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@id, \'RSTC_Close\')]') to be visible[22m

[2025-07-14T07:24:09.420Z] [INFO] Finished attempting to dismiss banners.
[2025-07-14T07:24:09.420Z] [INFO] Checking login status...
[2025-07-14T07:24:09.431Z] [INFO] Login status: Logged in (points element found).
[2025-07-14T07:24:09.432Z] [INFO] Already logged in.
[2025-07-14T07:24:09.432Z] [INFO] Checking initial points balance...
[2025-07-14T07:24:09.432Z] [INFO] Checking points balance...
[2025-07-14T07:24:10.760Z] [INFO] Attempting to dismiss banners...
[2025-07-14T07:24:15.769Z] [DEBUG] No banner found for XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@aria-label, \'Tutup\') or contains(@aria-label, \'Close\') or contains(text(), \'Tidak, terima kasih\') or contains(text(), \'No thanks\')]') to be visible[22m
[2m    15 × locator resolved to hidden <button tabindex="0" role="button" aria-label="Close" data-modal-close-button="" class="c-glyph glyph-cancel" ng-show="$ctrl.hasCloseButton" mee-progress-hidden="modalOperation" ng-attr-aria-label="{{$ctrl.meeAriaCloseButtonLabel}}"></button>[22m

[2025-07-14T07:24:20.781Z] [DEBUG] No banner found for XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//span[contains(@class, \'cancel\') or contains(@class, \'close\')]') to be visible[22m
[2m    15 × locator resolved to 2 elements. Proceeding with the first one: <span tabindex="0" role="button" aria-label="Close" class="autoredeem-close-cross" id="autoredeemWarningModalCloseCross" ng-click="$ctrl.hideWarningModal($event)" data-bi-id="rx-nav-autoredeem-banner-warning-close-cross">×</span>[22m

[2025-07-14T07:24:25.790Z] [DEBUG] No banner found for XPath: //a[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//a[contains(@aria-label, \'Tutup\') or contains(@aria-label, \'Close\')]') to be visible[22m

[2025-07-14T07:24:30.802Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'declineButton') or contains(@id, 'closeButton')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@id, \'declineButton\') or contains(@id, \'closeButton\')]') to be visible[22m

[2025-07-14T07:24:35.806Z] [DEBUG] No banner found for XPath: //div[contains(@class, 'lightbox-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'lightbox-close\')]') to be visible[22m

[2025-07-14T07:24:40.811Z] [DEBUG] No banner found for XPath: //div[contains(@class, 'common-banner-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'common-banner-close\')]') to be visible[22m

[2025-07-14T07:24:45.818Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'notification-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'notification-close\')]') to be visible[22m

[2025-07-14T07:24:50.824Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'close-button')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'close-button\')]') to be visible[22m

[2025-07-14T07:24:55.826Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'glif-dismiss')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'glif-dismiss\')]') to be visible[22m

[2025-07-14T07:25:00.827Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'me-Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'me-Close\')]') to be visible[22m

[2025-07-14T07:25:05.832Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'ub-emb-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'ub-emb-close\')]') to be visible[22m

[2025-07-14T07:25:10.836Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'ot-close-icon')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'ot-close-icon\')]') to be visible[22m

[2025-07-14T07:25:15.648Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'c-action-trigger') and contains(@aria-label, 'Close')] or click failed: page.waitForSelector: Target page, context or browser has been closed
Call log:
[2m  - waiting for locator('//button[contains(@class, \'c-action-trigger\') and contains(@aria-label, \'Close\')]') to be visible[22m

[2025-07-14T07:25:15.649Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'f-button') and contains(text(), 'No thanks')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.650Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'cookie-consent-button') and contains(text(), 'Accept')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.650Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'cookie-button') and contains(text(), 'Accept')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.651Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'onetrust-accept-btn-handler')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.652Z] [DEBUG] No banner found for XPath: //a[contains(@id, 'RSTC_Close')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.652Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'RSTC_Close')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.652Z] [INFO] Finished attempting to dismiss banners.
[2025-07-14T07:25:15.653Z] [DEBUG] Could not find points using XPath //div[contains(@class, 'points-balance')]//span[contains(@class, 'points-value')]: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.654Z] [DEBUG] Could not find points using XPath //div[contains(@aria-label, 'Microsoft Rewards points balance')]: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.654Z] [DEBUG] Could not find points using XPath //div[contains(@class, 'balance-container')]//span[contains(@class, 'points')]: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.655Z] [DEBUG] Could not find points using XPath //span[@id='id_rc']: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.655Z] [DEBUG] Could not find points using XPath //div[contains(@class, 'points-card')]//span[contains(@class, 'points')]: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:25:15.656Z] [WARNING] Could not find points balance on the page.
[2025-07-14T07:25:15.656Z] [INFO] Initial points balance: 0
[2025-07-14T07:25:15.656Z] [INFO] Starting 30 desktop searches...
[2025-07-14T07:25:15.657Z] [ERROR] Automation workflow failed: page.setViewportSize: Target page, context or browser has been closed
[2025-07-14T07:34:08.944Z] [INFO] Launching browser...
[2025-07-14T07:34:11.293Z] [INFO] Browser launched successfully.
[2025-07-14T07:34:11.293Z] [INFO] Attempting to log in...
[2025-07-14T07:34:14.497Z] [INFO] Attempting to dismiss banners...
[2025-07-14T07:34:19.508Z] [DEBUG] No banner found for XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@aria-label, \'Tutup\') or contains(@aria-label, \'Close\') or contains(text(), \'Tidak, terima kasih\') or contains(text(), \'No thanks\')]') to be visible[22m
[2m    15 × locator resolved to hidden <button tabindex="0" role="button" aria-label="Close" data-modal-close-button="" class="c-glyph glyph-cancel" ng-show="$ctrl.hasCloseButton" mee-progress-hidden="modalOperation" ng-attr-aria-label="{{$ctrl.meeAriaCloseButtonLabel}}"></button>[22m

[2025-07-14T07:34:24.512Z] [DEBUG] No banner found for XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//span[contains(@class, \'cancel\') or contains(@class, \'close\')]') to be visible[22m
[2m    15 × locator resolved to 2 elements. Proceeding with the first one: <span tabindex="0" role="button" aria-label="Close" class="autoredeem-close-cross" id="autoredeemWarningModalCloseCross" ng-click="$ctrl.hideWarningModal($event)" data-bi-id="rx-nav-autoredeem-banner-warning-close-cross">×</span>[22m

[2025-07-14T07:34:29.517Z] [DEBUG] No banner found for XPath: //a[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//a[contains(@aria-label, \'Tutup\') or contains(@aria-label, \'Close\')]') to be visible[22m

[2025-07-14T07:34:34.528Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'declineButton') or contains(@id, 'closeButton')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@id, \'declineButton\') or contains(@id, \'closeButton\')]') to be visible[22m

[2025-07-14T07:34:39.531Z] [DEBUG] No banner found for XPath: //div[contains(@class, 'lightbox-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'lightbox-close\')]') to be visible[22m

[2025-07-14T07:34:44.537Z] [DEBUG] No banner found for XPath: //div[contains(@class, 'common-banner-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'common-banner-close\')]') to be visible[22m

[2025-07-14T07:34:49.542Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'notification-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'notification-close\')]') to be visible[22m

[2025-07-14T07:34:54.548Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'close-button')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'close-button\')]') to be visible[22m

[2025-07-14T07:34:59.552Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'glif-dismiss')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'glif-dismiss\')]') to be visible[22m

[2025-07-14T07:35:04.564Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'me-Close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'me-Close\')]') to be visible[22m

[2025-07-14T07:35:09.569Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'ub-emb-close')] or click failed: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//button[contains(@class, \'ub-emb-close\')]') to be visible[22m

[2025-07-14T07:35:13.602Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'ot-close-icon')] or click failed: page.waitForSelector: Target page, context or browser has been closed
Call log:
[2m  - waiting for locator('//button[contains(@class, \'ot-close-icon\')]') to be visible[22m

[2025-07-14T07:35:13.603Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'c-action-trigger') and contains(@aria-label, 'Close')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:35:13.605Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'f-button') and contains(text(), 'No thanks')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:35:13.606Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'cookie-consent-button') and contains(text(), 'Accept')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:35:13.606Z] [DEBUG] No banner found for XPath: //button[contains(@class, 'cookie-button') and contains(text(), 'Accept')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:35:13.607Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'onetrust-accept-btn-handler')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:35:13.607Z] [DEBUG] No banner found for XPath: //a[contains(@id, 'RSTC_Close')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:35:13.608Z] [DEBUG] No banner found for XPath: //button[contains(@id, 'RSTC_Close')] or click failed: page.waitForSelector: Target page, context or browser has been closed
[2025-07-14T07:35:13.609Z] [INFO] Finished attempting to dismiss banners.
[2025-07-14T07:35:13.609Z] [INFO] Checking login status...
[2025-07-14T07:35:13.610Z] [DEBUG] Points element not found with XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span. Trying next.
[2025-07-14T07:35:13.611Z] [DEBUG] Points element not found with XPath: //mee-rewards-user-status-banner//mee-rewards-counter-animation/span. Trying next.
[2025-07-14T07:35:13.611Z] [DEBUG] Points element not found with XPath: //div[contains(@class, 'points-package')]//span[contains(@class, 'points-label')]. Trying next.
[2025-07-14T07:35:13.612Z] [INFO] Login status: Not logged in (points element not found).
[2025-07-14T07:35:13.612Z] [INFO] Navigating to Microsoft login page...
[2025-07-14T07:35:13.612Z] [ERROR] Automation workflow failed: page.goto: Target page, context or browser has been closed
[2025-07-14T07:38:43.431Z] [INFO] Launching browser...
[2025-07-14T07:38:43.686Z] [INFO] Browser launched successfully.
[2025-07-14T07:38:43.687Z] [INFO] Attempting to log in...
[2025-07-14T07:38:45.729Z] [INFO] Attempting to dismiss banners...
[2025-07-14T07:38:45.758Z] [DEBUG] Found banner element with XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]
[2025-07-14T07:38:45.769Z] [DEBUG] Banner found but not clickable (visible: false, enabled: true) with XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]
[2025-07-14T07:38:45.987Z] [DEBUG] Found banner element with XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')]
[2025-07-14T07:38:45.994Z] [DEBUG] Banner found but not clickable (visible: false, enabled: true) with XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')]
[2025-07-14T07:39:23.882Z] [DEBUG] Found banner element with XPath: //span[@id='autoredeemWarningModalCloseCross']
[2025-07-14T07:39:23.886Z] [DEBUG] Banner found but not clickable (visible: false, enabled: true) with XPath: //span[@id='autoredeemWarningModalCloseCross']
[2025-07-14T07:39:24.104Z] [DEBUG] Found banner element with XPath: //button[@data-modal-close-button]
[2025-07-14T07:39:24.111Z] [DEBUG] Banner found but not clickable (visible: false, enabled: true) with XPath: //button[@data-modal-close-button]
[2025-07-14T07:39:24.321Z] [INFO] Banner dismissal completed. Found: 4, Clicked: 0
[2025-07-14T07:39:24.321Z] [INFO] Checking login status...
[2025-07-14T07:39:24.326Z] [INFO] Login status: Logged in (points element found).
[2025-07-14T07:39:24.326Z] [INFO] Already logged in.
[2025-07-14T07:39:24.326Z] [INFO] Checking initial points balance...
[2025-07-14T07:39:24.326Z] [INFO] Checking points balance...
[2025-07-14T07:39:25.394Z] [INFO] Attempting to dismiss banners...
[2025-07-14T07:39:25.413Z] [DEBUG] Found banner element with XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]
[2025-07-14T07:39:25.420Z] [DEBUG] Banner found but not clickable (visible: false, enabled: true) with XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]
[2025-07-14T07:39:25.640Z] [DEBUG] Found banner element with XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')]
[2025-07-14T07:39:25.645Z] [DEBUG] Banner found but not clickable (visible: false, enabled: true) with XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')]
[2025-07-14T07:40:03.544Z] [DEBUG] Found banner element with XPath: //span[@id='autoredeemWarningModalCloseCross']
[2025-07-14T07:40:03.547Z] [DEBUG] Banner found but not clickable (visible: false, enabled: true) with XPath: //span[@id='autoredeemWarningModalCloseCross']
[2025-07-14T07:40:03.765Z] [DEBUG] Found banner element with XPath: //button[@data-modal-close-button]
[2025-07-14T07:40:03.771Z] [DEBUG] Banner found but not clickable (visible: false, enabled: true) with XPath: //button[@data-modal-close-button]
[2025-07-14T07:40:03.987Z] [INFO] Banner dismissal completed. Found: 4, Clicked: 0
[2025-07-14T07:40:09.002Z] [DEBUG] Could not find points using XPath //div[contains(@class, 'points-balance')]//span[contains(@class, 'points-value')]: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'points-balance\')]//span[contains(@class, \'points-value\')]') to be visible[22m

[2025-07-14T07:40:14.007Z] [DEBUG] Could not find points using XPath //div[contains(@aria-label, 'Microsoft Rewards points balance')]: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@aria-label, \'Microsoft Rewards points balance\')]') to be visible[22m

[2025-07-14T07:40:19.019Z] [DEBUG] Could not find points using XPath //div[contains(@class, 'balance-container')]//span[contains(@class, 'points')]: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'balance-container\')]//span[contains(@class, \'points\')]') to be visible[22m

[2025-07-14T07:40:24.023Z] [DEBUG] Could not find points using XPath //span[@id='id_rc']: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//span[@id=\'id_rc\']') to be visible[22m

[2025-07-14T07:40:29.026Z] [DEBUG] Could not find points using XPath //div[contains(@class, 'points-card')]//span[contains(@class, 'points')]: page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('//div[contains(@class, \'points-card\')]//span[contains(@class, \'points\')]') to be visible[22m

[2025-07-14T07:40:29.027Z] [WARNING] Could not find points balance on the page.
[2025-07-14T07:40:29.028Z] [INFO] Initial points balance: 0
[2025-07-14T07:40:29.029Z] [INFO] Starting 30 desktop searches...
[2025-07-14T07:40:29.092Z] [ERROR] Automation workflow failed: this.page.setUserAgent is not a function
[2025-07-14T08:01:43.685Z] [INFO] Launching browser...
[2025-07-14T08:01:44.225Z] [INFO] Browser launched successfully.
[2025-07-14T08:01:44.225Z] [INFO] Attempting to log in...
[2025-07-14T08:01:48.415Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:01:48.444Z] [DEBUG] Found banner element with XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]
[2025-07-14T08:01:48.454Z] [DEBUG] Trying force click on hidden but enabled banner: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]
[2025-07-14T08:01:48.458Z] [DEBUG] Force click on hidden element failed: elementHandle.click: Element is not visible
Call log:
[2m  - attempting click action[22m
[2m    - scrolling into view if needed[22m

[2025-07-14T08:01:48.664Z] [DEBUG] Found banner element with XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')]
[2025-07-14T08:01:48.668Z] [DEBUG] Trying force click on hidden but enabled banner: //span[contains(@class, 'cancel') or contains(@class, 'close')]
[2025-07-14T08:01:48.671Z] [DEBUG] Force click on hidden element failed: elementHandle.click: Element is not visible
Call log:
[2m  - attempting click action[22m
[2m    - scrolling into view if needed[22m

[2025-07-14T08:02:26.297Z] [DEBUG] Found banner element with XPath: //span[@id='autoredeemWarningModalCloseCross']
[2025-07-14T08:02:26.301Z] [DEBUG] Trying force click on hidden but enabled banner: //span[@id='autoredeemWarningModalCloseCross']
[2025-07-14T08:02:26.303Z] [DEBUG] Force click on hidden element failed: elementHandle.click: Element is not visible
Call log:
[2m  - attempting click action[22m
[2m    - scrolling into view if needed[22m

[2025-07-14T08:02:26.507Z] [DEBUG] Found banner element with XPath: //button[@data-modal-close-button]
[2025-07-14T08:02:26.511Z] [DEBUG] Trying force click on hidden but enabled banner: //button[@data-modal-close-button]
[2025-07-14T08:02:26.512Z] [DEBUG] Force click on hidden element failed: elementHandle.click: Element is not visible
Call log:
[2m  - attempting click action[22m
[2m    - scrolling into view if needed[22m

[2025-07-14T08:02:26.713Z] [INFO] Banner dismissal completed. Found: 4, Clicked: 0
[2025-07-14T08:02:26.713Z] [INFO] Checking login status...
[2025-07-14T08:02:26.718Z] [INFO] Login status: Logged in (points element found).
[2025-07-14T08:02:26.719Z] [INFO] Already logged in.
[2025-07-14T08:02:26.719Z] [INFO] Checking initial points balance...
[2025-07-14T08:02:26.720Z] [INFO] Checking points balance...
[2025-07-14T08:02:29.725Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:02:29.742Z] [DEBUG] Found banner element with XPath: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]
[2025-07-14T08:02:29.749Z] [DEBUG] Trying force click on hidden but enabled banner: //button[contains(@aria-label, 'Tutup') or contains(@aria-label, 'Close') or contains(text(), 'Tidak, terima kasih') or contains(text(), 'No thanks')]
[2025-07-14T08:02:29.751Z] [DEBUG] Force click on hidden element failed: elementHandle.click: Element is not visible
Call log:
[2m  - attempting click action[22m
[2m    - scrolling into view if needed[22m

[2025-07-14T08:02:29.956Z] [DEBUG] Found banner element with XPath: //span[contains(@class, 'cancel') or contains(@class, 'close')]
[2025-07-14T08:02:29.959Z] [DEBUG] Trying force click on hidden but enabled banner: //span[contains(@class, 'cancel') or contains(@class, 'close')]
[2025-07-14T08:02:29.961Z] [DEBUG] Force click on hidden element failed: elementHandle.click: Element is not visible
Call log:
[2m  - attempting click action[22m
[2m    - scrolling into view if needed[22m

[2025-07-14T08:03:07.591Z] [DEBUG] Found banner element with XPath: //span[@id='autoredeemWarningModalCloseCross']
[2025-07-14T08:03:07.595Z] [DEBUG] Trying force click on hidden but enabled banner: //span[@id='autoredeemWarningModalCloseCross']
[2025-07-14T08:03:07.596Z] [DEBUG] Force click on hidden element failed: elementHandle.click: Element is not visible
Call log:
[2m  - attempting click action[22m
[2m    - scrolling into view if needed[22m

[2025-07-14T08:03:07.804Z] [DEBUG] Found banner element with XPath: //button[@data-modal-close-button]
[2025-07-14T08:03:07.812Z] [DEBUG] Trying force click on hidden but enabled banner: //button[@data-modal-close-button]
[2025-07-14T08:03:07.815Z] [DEBUG] Force click on hidden element failed: elementHandle.click: Element is not visible
Call log:
[2m  - attempting click action[22m
[2m    - scrolling into view if needed[22m

[2025-07-14T08:03:08.016Z] [INFO] Banner dismissal completed. Found: 4, Clicked: 0
[2025-07-14T08:03:08.029Z] [DEBUG] Found potential points text: "414" using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-14T08:03:08.029Z] [INFO] Found points balance: 414 using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-14T08:03:08.029Z] [INFO] Initial points balance: 414
[2025-07-14T08:03:08.029Z] [INFO] Starting 30 desktop searches...
[2025-07-14T08:03:08.067Z] [INFO] Set user agent to desktop.
[2025-07-14T08:03:08.067Z] [INFO] Performing desktop search 1/30 for: "sports scores"
[2025-07-14T08:03:08.387Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:03:54.859Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:04:04.285Z] [INFO] Performing desktop search 2/30 for: "car models"
[2025-07-14T08:04:04.491Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:04:50.716Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:04:58.211Z] [INFO] Performing desktop search 3/30 for: "history events"
[2025-07-14T08:04:58.407Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:05:44.649Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:05:51.597Z] [INFO] Performing desktop search 4/30 for: "technology trends"
[2025-07-14T08:05:51.768Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:06:38.008Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:06:45.413Z] [INFO] Performing desktop search 5/30 for: "cooking recipes"
[2025-07-14T08:06:45.640Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:07:31.869Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:07:41.063Z] [INFO] Performing desktop search 6/30 for: "artificial intelligence"
[2025-07-14T08:07:41.251Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:08:27.477Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:08:35.419Z] [INFO] Performing desktop search 7/30 for: "fitness routines"
[2025-07-14T08:08:35.613Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:09:21.854Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:09:31.407Z] [INFO] Performing desktop search 8/30 for: "cloud computing"
[2025-07-14T08:09:31.606Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:10:17.855Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:10:26.138Z] [INFO] Performing desktop search 9/30 for: "fashion styles"
[2025-07-14T08:10:26.324Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:11:12.562Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:11:21.808Z] [INFO] Performing desktop search 10/30 for: "cloud computing"
[2025-07-14T08:11:21.992Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:12:08.228Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:12:14.904Z] [INFO] Performing desktop search 11/30 for: "weather forecast"
[2025-07-14T08:12:15.161Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:13:01.387Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:13:11.492Z] [INFO] Performing desktop search 12/30 for: "machine learning"
[2025-07-14T08:13:11.667Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:13:57.914Z] [INFO] Banner dismissal completed. Found: 0, Clicked: 0
[2025-07-14T08:14:06.310Z] [INFO] Performing desktop search 13/30 for: "music charts"
[2025-07-14T08:14:06.578Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:14:34.409Z] [ERROR] Search failed for "music charts": page.waitForTimeout: Target page, context or browser has been closed
[2025-07-14T08:14:34.409Z] [INFO] Performing desktop search 14/30 for: "artificial intelligence"
[2025-07-14T08:14:34.410Z] [ERROR] Search failed for "artificial intelligence": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.410Z] [INFO] Performing desktop search 15/30 for: "animal facts"
[2025-07-14T08:14:34.410Z] [ERROR] Search failed for "animal facts": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.410Z] [INFO] Performing desktop search 16/30 for: "cyber security"
[2025-07-14T08:14:34.410Z] [ERROR] Search failed for "cyber security": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.411Z] [INFO] Performing desktop search 17/30 for: "machine learning"
[2025-07-14T08:14:34.411Z] [ERROR] Search failed for "machine learning": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.411Z] [INFO] Performing desktop search 18/30 for: "plant types"
[2025-07-14T08:14:34.411Z] [ERROR] Search failed for "plant types": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.411Z] [INFO] Performing desktop search 19/30 for: "famous quotes"
[2025-07-14T08:14:34.411Z] [ERROR] Search failed for "famous quotes": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.411Z] [INFO] Performing desktop search 20/30 for: "space exploration"
[2025-07-14T08:14:34.412Z] [ERROR] Search failed for "space exploration": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.412Z] [INFO] Performing desktop search 21/30 for: "sports scores"
[2025-07-14T08:14:34.412Z] [ERROR] Search failed for "sports scores": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.412Z] [INFO] Performing desktop search 22/30 for: "history events"
[2025-07-14T08:14:34.413Z] [ERROR] Search failed for "history events": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.413Z] [INFO] Performing desktop search 23/30 for: "machine learning"
[2025-07-14T08:14:34.413Z] [ERROR] Search failed for "machine learning": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.413Z] [INFO] Performing desktop search 24/30 for: "artificial intelligence"
[2025-07-14T08:14:34.414Z] [ERROR] Search failed for "artificial intelligence": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.414Z] [INFO] Performing desktop search 25/30 for: "web development"
[2025-07-14T08:14:34.414Z] [ERROR] Search failed for "web development": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.414Z] [INFO] Performing desktop search 26/30 for: "famous quotes"
[2025-07-14T08:14:34.415Z] [ERROR] Search failed for "famous quotes": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.415Z] [INFO] Performing desktop search 27/30 for: "web development"
[2025-07-14T08:14:34.415Z] [ERROR] Search failed for "web development": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.416Z] [INFO] Performing desktop search 28/30 for: "travel destinations"
[2025-07-14T08:14:34.416Z] [ERROR] Search failed for "travel destinations": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.416Z] [INFO] Performing desktop search 29/30 for: "art exhibitions"
[2025-07-14T08:14:34.417Z] [ERROR] Search failed for "art exhibitions": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.417Z] [INFO] Performing desktop search 30/30 for: "art exhibitions"
[2025-07-14T08:14:34.417Z] [ERROR] Search failed for "art exhibitions": page.goto: Target page, context or browser has been closed
[2025-07-14T08:14:34.417Z] [INFO] Finished 30 desktop searches.
[2025-07-14T08:14:34.417Z] [INFO] Starting 20 mobile searches...
[2025-07-14T08:14:34.419Z] [ERROR] Automation workflow failed: page.setViewportSize: Target page, context or browser has been closed
[2025-07-14T08:14:37.865Z] [INFO] Launching browser...
[2025-07-14T08:14:38.148Z] [INFO] Browser launched successfully.
[2025-07-14T08:14:38.148Z] [INFO] Attempting to log in...
[2025-07-14T08:14:41.215Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:14:46.275Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:14:46.276Z] [INFO] Checking login status...
[2025-07-14T08:14:46.288Z] [INFO] Login status: Logged in (points element found).
[2025-07-14T08:14:46.288Z] [INFO] Already logged in.
[2025-07-14T08:14:46.288Z] [INFO] Checking initial points balance...
[2025-07-14T08:14:46.288Z] [INFO] Checking points balance...
[2025-07-14T08:14:47.488Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:14:52.532Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:14:52.547Z] [DEBUG] Found potential points text: "426" using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-14T08:14:52.547Z] [INFO] Found points balance: 426 using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-14T08:14:52.547Z] [INFO] Initial points balance: 426
[2025-07-14T08:14:52.547Z] [INFO] Starting 30 desktop searches...
[2025-07-14T08:14:52.579Z] [INFO] Set user agent to desktop.
[2025-07-14T08:14:52.580Z] [INFO] Performing desktop search 1/30 for: "music charts"
[2025-07-14T08:14:52.761Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:14:57.810Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:15:04.005Z] [INFO] Performing desktop search 2/30 for: "art exhibitions"
[2025-07-14T08:15:14.317Z] [INFO] Performing desktop search 3/30 for: "stock market news"
[2025-07-14T08:15:24.076Z] [INFO] Performing desktop search 4/30 for: "travel destinations"
[2025-07-14T08:15:33.572Z] [INFO] Performing desktop search 5/30 for: "sports scores"
[2025-07-14T08:15:45.098Z] [INFO] Performing desktop search 6/30 for: "currency exchange rates"
[2025-07-14T08:15:52.447Z] [INFO] Performing desktop search 7/30 for: "art exhibitions"
[2025-07-14T08:15:59.927Z] [INFO] Performing desktop search 8/30 for: "stock market news"
[2025-07-14T08:16:10.225Z] [INFO] Performing desktop search 9/30 for: "sports scores"
[2025-07-14T08:16:18.634Z] [INFO] Performing desktop search 10/30 for: "artificial intelligence"
[2025-07-14T08:16:26.175Z] [INFO] Performing desktop search 11/30 for: "travel destinations"
[2025-07-14T08:16:26.361Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:16:31.394Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:16:38.418Z] [INFO] Performing desktop search 12/30 for: "cyber security"
[2025-07-14T08:16:47.367Z] [INFO] Performing desktop search 13/30 for: "web development"
[2025-07-14T08:16:56.397Z] [INFO] Performing desktop search 14/30 for: "weather forecast"
[2025-07-14T08:17:03.986Z] [INFO] Performing desktop search 15/30 for: "sports scores"
[2025-07-14T08:17:11.711Z] [INFO] Performing desktop search 16/30 for: "history events"
[2025-07-14T08:17:19.818Z] [INFO] Performing desktop search 17/30 for: "machine learning"
[2025-07-14T08:17:29.892Z] [INFO] Performing desktop search 18/30 for: "animal facts"
[2025-07-14T08:17:38.142Z] [INFO] Performing desktop search 19/30 for: "web development"
[2025-07-14T08:17:47.467Z] [INFO] Performing desktop search 20/30 for: "artificial intelligence"
[2025-07-14T08:17:56.969Z] [INFO] Performing desktop search 21/30 for: "fitness routines"
[2025-07-14T08:17:57.157Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:18:02.190Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:18:08.268Z] [INFO] Performing desktop search 22/30 for: "web development"
[2025-07-14T08:18:17.326Z] [INFO] Performing desktop search 23/30 for: "plant types"
[2025-07-14T08:18:24.953Z] [INFO] Performing desktop search 24/30 for: "animal facts"
[2025-07-14T08:18:32.972Z] [INFO] Performing desktop search 25/30 for: "latest news"
[2025-07-14T08:18:48.203Z] [ERROR] Search failed for "latest news": page.waitForLoadState: Timeout 15000ms exceeded.
[2025-07-14T08:18:48.204Z] [INFO] Performing desktop search 26/30 for: "plant types"
[2025-07-14T08:19:03.489Z] [ERROR] Search failed for "plant types": page.waitForLoadState: Timeout 15000ms exceeded.
[2025-07-14T08:19:03.490Z] [INFO] Performing desktop search 27/30 for: "data science"
[2025-07-14T08:19:18.706Z] [ERROR] Search failed for "data science": page.waitForLoadState: Timeout 15000ms exceeded.
[2025-07-14T08:19:18.707Z] [INFO] Performing desktop search 28/30 for: "cloud computing"
[2025-07-14T08:19:33.981Z] [ERROR] Search failed for "cloud computing": page.waitForLoadState: Timeout 15000ms exceeded.
[2025-07-14T08:19:33.981Z] [INFO] Performing desktop search 29/30 for: "latest news"
[2025-07-14T08:19:43.119Z] [INFO] Performing desktop search 30/30 for: "machine learning"
[2025-07-14T08:19:58.352Z] [ERROR] Search failed for "machine learning": page.waitForLoadState: Timeout 15000ms exceeded.
[2025-07-14T08:19:58.352Z] [INFO] Finished 30 desktop searches.
[2025-07-14T08:19:58.353Z] [INFO] Starting 20 mobile searches...
[2025-07-14T08:19:58.532Z] [INFO] Set user agent to mobile.
[2025-07-14T08:19:58.533Z] [INFO] Performing mobile search 1/20 for: "cyber security"
[2025-07-14T08:19:58.799Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:20:03.831Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:20:10.494Z] [INFO] Performing mobile search 2/20 for: "weather forecast"
[2025-07-14T08:20:17.823Z] [INFO] Performing mobile search 3/20 for: "data science"
[2025-07-14T08:20:27.434Z] [INFO] Performing mobile search 4/20 for: "music charts"
[2025-07-14T08:20:35.531Z] [INFO] Performing mobile search 5/20 for: "health tips"
[2025-07-14T08:20:43.037Z] [INFO] Performing mobile search 6/20 for: "latest news"
[2025-07-14T08:20:49.942Z] [INFO] Performing mobile search 7/20 for: "weather forecast"
[2025-07-14T08:20:56.968Z] [INFO] Performing mobile search 8/20 for: "art exhibitions"
[2025-07-14T08:21:04.333Z] [INFO] Performing mobile search 9/20 for: "science facts"
[2025-07-14T08:21:12.631Z] [INFO] Performing mobile search 10/20 for: "famous quotes"
[2025-07-14T08:21:20.106Z] [INFO] Performing mobile search 11/20 for: "cyber security"
[2025-07-14T08:21:20.530Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:21:25.571Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:21:32.060Z] [INFO] Performing mobile search 12/20 for: "car models"
[2025-07-14T08:21:38.264Z] [INFO] Performing mobile search 13/20 for: "world capitals"
[2025-07-14T08:21:45.317Z] [INFO] Performing mobile search 14/20 for: "latest news"
[2025-07-14T08:21:52.200Z] [INFO] Performing mobile search 15/20 for: "book reviews"
[2025-07-14T08:21:59.274Z] [INFO] Performing mobile search 16/20 for: "health tips"
[2025-07-14T08:22:05.262Z] [INFO] Performing mobile search 17/20 for: "book reviews"
[2025-07-14T08:22:12.885Z] [INFO] Performing mobile search 18/20 for: "world capitals"
[2025-07-14T08:22:20.678Z] [INFO] Performing mobile search 19/20 for: "web development"
[2025-07-14T08:22:29.109Z] [INFO] Performing mobile search 20/20 for: "world capitals"
[2025-07-14T08:22:36.678Z] [INFO] Finished 20 mobile searches.
[2025-07-14T08:22:36.678Z] [INFO] Starting daily set activities...
[2025-07-14T08:22:38.568Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:22:43.601Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:22:53.603Z] [ERROR] Error during initial scan for daily set activities: page.waitForSelector: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('//*[@id=\'daily-set-section\']') to be visible[22m
. Skipping.
[2025-07-14T08:22:53.606Z] [INFO] Starting other activities...
[2025-07-14T08:22:54.704Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:22:59.748Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:23:09.750Z] [ERROR] Error during initial scan for other activities: page.waitForSelector: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('//*[@id=\'more-earning-section\']') to be visible[22m
. Skipping.
[2025-07-14T08:23:09.751Z] [INFO] Checking final points balance...
[2025-07-14T08:23:09.751Z] [INFO] Checking points balance...
[2025-07-14T08:23:10.743Z] [INFO] Attempting to dismiss banners...
[2025-07-14T08:23:15.780Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-14T08:23:15.794Z] [DEBUG] Found potential points text: "426" using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-14T08:23:15.795Z] [INFO] Found points balance: 426 using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-14T08:23:15.795Z] [INFO] Final points balance: 426
[2025-07-14T08:23:15.795Z] [INFO] Automation workflow completed.
[2025-07-16T14:48:50.254Z] [INFO] Launching browser...
[2025-07-16T14:48:50.707Z] [INFO] Browser launched successfully.
[2025-07-16T14:48:50.707Z] [INFO] Attempting to log in...
[2025-07-16T14:48:54.018Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:48:59.099Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:48:59.100Z] [INFO] Checking login status...
[2025-07-16T14:48:59.108Z] [INFO] Login status: Logged in (points element found).
[2025-07-16T14:48:59.108Z] [INFO] Already logged in.
[2025-07-16T14:48:59.108Z] [INFO] Checking initial points balance...
[2025-07-16T14:48:59.108Z] [INFO] Checking points balance...
[2025-07-16T14:49:00.183Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:49:05.254Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:49:05.272Z] [DEBUG] Found potential points text: "495" using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-16T14:49:05.272Z] [INFO] Found points balance: 495 using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-16T14:49:05.272Z] [INFO] Initial points balance: 495
[2025-07-16T14:49:05.272Z] [INFO] Starting 30 desktop searches...
[2025-07-16T14:49:05.301Z] [INFO] Set user agent to desktop.
[2025-07-16T14:49:05.301Z] [INFO] Performing desktop search 1/30 for: "history events"
[2025-07-16T14:49:05.542Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:49:10.599Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:49:19.937Z] [INFO] Performing desktop search 2/30 for: "art exhibitions"
[2025-07-16T14:49:29.681Z] [INFO] Performing desktop search 3/30 for: "programming languages"
[2025-07-16T14:49:36.067Z] [INFO] Performing desktop search 4/30 for: "cloud computing"
[2025-07-16T14:49:45.610Z] [INFO] Performing desktop search 5/30 for: "science facts"
[2025-07-16T14:49:55.049Z] [INFO] Performing desktop search 6/30 for: "latest news"
[2025-07-16T14:50:04.722Z] [INFO] Performing desktop search 7/30 for: "latest news"
[2025-07-16T14:50:13.914Z] [INFO] Performing desktop search 8/30 for: "travel destinations"
[2025-07-16T14:50:23.799Z] [INFO] Performing desktop search 9/30 for: "movie releases"
[2025-07-16T14:50:32.549Z] [INFO] Performing desktop search 10/30 for: "animal facts"
[2025-07-16T14:50:43.008Z] [INFO] Performing desktop search 11/30 for: "art exhibitions"
[2025-07-16T14:50:43.170Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:50:48.320Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:50:58.472Z] [INFO] Performing desktop search 12/30 for: "world capitals"
[2025-07-16T14:51:06.200Z] [INFO] Performing desktop search 13/30 for: "cooking recipes"
[2025-07-16T14:51:16.513Z] [INFO] Performing desktop search 14/30 for: "machine learning"
[2025-07-16T14:51:24.401Z] [INFO] Performing desktop search 15/30 for: "machine learning"
[2025-07-16T14:51:32.090Z] [INFO] Performing desktop search 16/30 for: "movie releases"
[2025-07-16T14:51:40.861Z] [INFO] Performing desktop search 17/30 for: "book reviews"
[2025-07-16T14:51:49.636Z] [INFO] Performing desktop search 18/30 for: "web development"
[2025-07-16T14:51:58.808Z] [INFO] Performing desktop search 19/30 for: "cloud computing"
[2025-07-16T14:52:08.042Z] [INFO] Performing desktop search 20/30 for: "fitness routines"
[2025-07-16T14:52:15.919Z] [INFO] Performing desktop search 21/30 for: "latest news"
[2025-07-16T14:52:16.101Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:52:21.166Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:52:29.646Z] [INFO] Performing desktop search 22/30 for: "history events"
[2025-07-16T14:52:37.932Z] [INFO] Performing desktop search 23/30 for: "travel destinations"
[2025-07-16T14:52:47.193Z] [INFO] Performing desktop search 24/30 for: "cloud computing"
[2025-07-16T14:52:57.538Z] [INFO] Performing desktop search 25/30 for: "art exhibitions"
[2025-07-16T14:53:07.816Z] [INFO] Performing desktop search 26/30 for: "programming languages"
[2025-07-16T14:53:15.919Z] [INFO] Performing desktop search 27/30 for: "movie releases"
[2025-07-16T14:53:24.126Z] [INFO] Performing desktop search 28/30 for: "travel destinations"
[2025-07-16T14:53:33.925Z] [INFO] Performing desktop search 29/30 for: "animal facts"
[2025-07-16T14:53:42.645Z] [INFO] Performing desktop search 30/30 for: "space exploration"
[2025-07-16T14:53:50.439Z] [INFO] Finished 30 desktop searches.
[2025-07-16T14:53:50.439Z] [INFO] Starting 20 mobile searches...
[2025-07-16T14:53:50.498Z] [INFO] Set user agent to mobile.
[2025-07-16T14:53:50.498Z] [INFO] Performing mobile search 1/20 for: "health tips"
[2025-07-16T14:53:50.707Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:53:55.772Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:54:02.324Z] [ERROR] Search failed for "health tips": page.waitForTimeout: Target page, context or browser has been closed
[2025-07-16T14:54:02.324Z] [INFO] Performing mobile search 2/20 for: "technology trends"
[2025-07-16T14:54:02.328Z] [ERROR] Search failed for "technology trends": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.329Z] [INFO] Performing mobile search 3/20 for: "stock market news"
[2025-07-16T14:54:02.330Z] [ERROR] Search failed for "stock market news": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.331Z] [INFO] Performing mobile search 4/20 for: "web development"
[2025-07-16T14:54:02.331Z] [ERROR] Search failed for "web development": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.331Z] [INFO] Performing mobile search 5/20 for: "artificial intelligence"
[2025-07-16T14:54:02.332Z] [ERROR] Search failed for "artificial intelligence": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.332Z] [INFO] Performing mobile search 6/20 for: "fitness routines"
[2025-07-16T14:54:02.334Z] [ERROR] Search failed for "fitness routines": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.334Z] [INFO] Performing mobile search 7/20 for: "sports scores"
[2025-07-16T14:54:02.335Z] [ERROR] Search failed for "sports scores": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.335Z] [INFO] Performing mobile search 8/20 for: "health tips"
[2025-07-16T14:54:02.336Z] [ERROR] Search failed for "health tips": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.336Z] [INFO] Performing mobile search 9/20 for: "cooking recipes"
[2025-07-16T14:54:02.337Z] [ERROR] Search failed for "cooking recipes": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.337Z] [INFO] Performing mobile search 10/20 for: "artificial intelligence"
[2025-07-16T14:54:02.337Z] [ERROR] Search failed for "artificial intelligence": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.337Z] [INFO] Performing mobile search 11/20 for: "sports scores"
[2025-07-16T14:54:02.338Z] [ERROR] Search failed for "sports scores": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.338Z] [INFO] Performing mobile search 12/20 for: "history events"
[2025-07-16T14:54:02.339Z] [ERROR] Search failed for "history events": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.339Z] [INFO] Performing mobile search 13/20 for: "cloud computing"
[2025-07-16T14:54:02.340Z] [ERROR] Search failed for "cloud computing": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.340Z] [INFO] Performing mobile search 14/20 for: "travel destinations"
[2025-07-16T14:54:02.340Z] [ERROR] Search failed for "travel destinations": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.341Z] [INFO] Performing mobile search 15/20 for: "car models"
[2025-07-16T14:54:02.341Z] [ERROR] Search failed for "car models": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.341Z] [INFO] Performing mobile search 16/20 for: "history events"
[2025-07-16T14:54:02.342Z] [ERROR] Search failed for "history events": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.342Z] [INFO] Performing mobile search 17/20 for: "famous quotes"
[2025-07-16T14:54:02.342Z] [ERROR] Search failed for "famous quotes": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.342Z] [INFO] Performing mobile search 18/20 for: "space exploration"
[2025-07-16T14:54:02.343Z] [ERROR] Search failed for "space exploration": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.343Z] [INFO] Performing mobile search 19/20 for: "travel destinations"
[2025-07-16T14:54:02.343Z] [ERROR] Search failed for "travel destinations": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.344Z] [INFO] Performing mobile search 20/20 for: "programming languages"
[2025-07-16T14:54:02.344Z] [ERROR] Search failed for "programming languages": page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:02.344Z] [INFO] Finished 20 mobile searches.
[2025-07-16T14:54:02.344Z] [INFO] Starting daily set activities...
[2025-07-16T14:54:02.345Z] [ERROR] Automation workflow failed: page.goto: Target page, context or browser has been closed
[2025-07-16T14:54:04.708Z] [INFO] Launching browser...
[2025-07-16T14:54:04.969Z] [INFO] Browser launched successfully.
[2025-07-16T14:54:04.969Z] [INFO] Attempting to log in...
[2025-07-16T14:54:07.289Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:54:12.350Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:54:12.350Z] [INFO] Checking login status...
[2025-07-16T14:54:12.355Z] [INFO] Login status: Logged in (points element found).
[2025-07-16T14:54:12.356Z] [INFO] Already logged in.
[2025-07-16T14:54:12.356Z] [INFO] Checking initial points balance...
[2025-07-16T14:54:12.356Z] [INFO] Checking points balance...
[2025-07-16T14:54:13.376Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:54:18.455Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:54:18.478Z] [DEBUG] Found potential points text: "513" using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-16T14:54:18.478Z] [INFO] Found points balance: 513 using XPath: //mee-rewards-user-status-banner//p[contains(@class, 'pointsValue')]//span
[2025-07-16T14:54:18.478Z] [INFO] Initial points balance: 513
[2025-07-16T14:54:18.479Z] [INFO] Starting 30 desktop searches...
[2025-07-16T14:54:18.479Z] [INFO] Available search terms: 124, Used so far: 0
[2025-07-16T14:54:18.509Z] [INFO] Set user agent to desktop.
[2025-07-16T14:54:18.509Z] [INFO] Performing desktop search 1/30 for: "baking tips"
[2025-07-16T14:54:18.704Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:54:23.773Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:54:33.098Z] [INFO] Performing desktop search 2/30 for: "environmental news"
[2025-07-16T14:54:42.856Z] [INFO] Performing desktop search 3/30 for: "world news"
[2025-07-16T14:54:50.038Z] [INFO] Performing desktop search 4/30 for: "fitness equipment"
[2025-07-16T14:54:59.573Z] [INFO] Performing desktop search 5/30 for: "celebrity news"
[2025-07-16T14:55:09.531Z] [INFO] Performing desktop search 6/30 for: "historical events"
[2025-07-16T14:55:17.688Z] [INFO] Performing desktop search 7/30 for: "sports nutrition"
[2025-07-16T14:55:27.371Z] [INFO] Performing desktop search 8/30 for: "hiking trails"
[2025-07-16T14:55:35.368Z] [INFO] Performing desktop search 9/30 for: "vegetarian recipes"
[2025-07-16T14:55:45.342Z] [INFO] Performing desktop search 10/30 for: "car models"
[2025-07-16T14:55:53.635Z] [INFO] Performing desktop search 11/30 for: "economic trends"
[2025-07-16T14:55:53.816Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:55:58.897Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:56:06.144Z] [INFO] Performing desktop search 12/30 for: "mental health"
[2025-07-16T14:56:16.914Z] [INFO] Performing desktop search 13/30 for: "forest conservation"
[2025-07-16T14:56:28.813Z] [INFO] Performing desktop search 14/30 for: "tennis matches"
[2025-07-16T14:56:36.516Z] [INFO] Performing desktop search 15/30 for: "international cuisine"
[2025-07-16T14:56:43.982Z] [INFO] Performing desktop search 16/30 for: "breaking news"
[2025-07-16T14:56:53.441Z] [INFO] Performing desktop search 17/30 for: "climate change"
[2025-07-16T14:57:02.190Z] [INFO] Performing desktop search 18/30 for: "wellness tips"
[2025-07-16T14:57:11.360Z] [INFO] Performing desktop search 19/30 for: "sports scores"
[2025-07-16T14:57:18.735Z] [INFO] Performing desktop search 20/30 for: "hotels"
[2025-07-16T14:57:27.311Z] [INFO] Performing desktop search 21/30 for: "fuel prices"
[2025-07-16T14:57:27.517Z] [INFO] Attempting to dismiss banners...
[2025-07-16T14:57:32.572Z] [INFO] Banner dismissal completed. Clicked: 0
[2025-07-16T14:57:39.666Z] [INFO] Performing desktop search 22/30 for: "famous quotes"
[2025-07-16T14:57:47.127Z] [INFO] Performing desktop search 23/30 for: "data science"
[2025-07-16T14:57:55.189Z] [INFO] Performing desktop search 24/30 for: "basketball news"
[2025-07-16T14:58:01.915Z] [ERROR] Search failed for "basketball news": page.waitForTimeout: Target page, context or browser has been closed
[2025-07-16T14:58:01.915Z] [INFO] Performing desktop search 25/30 for: "artificial intelligence"
[2025-07-16T14:58:01.916Z] [ERROR] Search failed for "artificial intelligence": page.goto: Target page, context or browser has been closed
[2025-07-16T14:58:01.916Z] [INFO] Performing desktop search 26/30 for: "green technology"
[2025-07-16T14:58:01.916Z] [ERROR] Search failed for "green technology": page.goto: Target page, context or browser has been closed
[2025-07-16T14:58:01.916Z] [INFO] Performing desktop search 27/30 for: "football results"
[2025-07-16T14:58:01.917Z] [ERROR] Search failed for "football results": page.goto: Target page, context or browser has been closed
[2025-07-16T14:58:01.917Z] [INFO] Performing desktop search 28/30 for: "business news"
[2025-07-16T14:58:01.917Z] [ERROR] Search failed for "business news": page.goto: Target page, context or browser has been closed
[2025-07-16T14:58:01.917Z] [INFO] Performing desktop search 29/30 for: "car reviews"
[2025-07-16T14:58:01.918Z] [ERROR] Search failed for "car reviews": page.goto: Target page, context or browser has been closed
[2025-07-16T14:58:01.918Z] [INFO] Performing desktop search 30/30 for: "museum exhibits"
[2025-07-16T14:58:01.918Z] [ERROR] Search failed for "museum exhibits": page.goto: Target page, context or browser has been closed
[2025-07-16T14:58:01.918Z] [INFO] Finished 30 desktop searches.
[2025-07-16T14:58:01.918Z] [INFO] Starting 20 mobile searches...
[2025-07-16T14:58:01.919Z] [INFO] Available search terms: 124, Used so far: 30
[2025-07-16T14:58:01.919Z] [ERROR] Automation workflow failed: page.setViewportSize: Target page, context or browser has been closed
