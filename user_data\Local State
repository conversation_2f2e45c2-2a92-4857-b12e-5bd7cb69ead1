{"abusive_adblocker_etag": "\"5E25271B8190D943537AD3FDB50874FC133E8B4A00380E2A6A888D63386F728B\"", "app_defaults": {"os_activation_state": true}, "apps_count_check_time": "13396951379303229", "autofill": {"ablation_seed": "288dZLiW8F0="}, "breadcrumbs": {"enabled": true, "enabled_time": "13396951348597690"}, "cloud_config_service_v2": {"config_observers_json_md5": "+HmVPbOkKWVe/czHWtoB4w==", "config_observers_json_semantic_version": "25.4.14.1", "config_observers_json_semantic_version_last_r_date": "13396951408889039", "last_update_checking_time": "13396953763934847", "observers": {"CloudConfigServiceV1MobileConfigObserver": {"md5": "BXpFGzjRKBQjesTgtswcrw==", "url": "https://edge-mobile-static.azureedge.net/eccp/get?settenant=edge-config&setplatform=win&setmkt=en-US&setchannel=stable"}, "operation_config": {"md5": "xsYKv3QVNCVN/9emtaICNA==", "url": "https://edge-cloud-resource-static.azureedge.net/default/operation_config/default.json"}}}, "default_browser": {"browser_name_enum": 13}, "desktop_session_duration_tracker": {"last_session_end_timestamp": "0"}, "dual_engine": {"ie_mode_consumer_enabled_any_profile": true, "ie_mode_consumer_last_seen_any_profile": "13396954477984708", "ie_to_edge": {"redirection_mode": 2}}, "edge": {"app_user_model_id": "MSEdge", "manageability": {"edge_last_active_report_time": "13396951354279959", "edge_last_active_time": "13396954318398072"}, "perf_center": {"efficiency_mode_v2_is_active": false, "perf_game_mode": true, "performance_mode": 3, "performance_mode_is_on": false}, "tab_stabs": {"closed_without_unfreeze_never_unfrozen": 0, "closed_without_unfreeze_previously_unfrozen": 0, "discard_without_unfreeze_never_unfrozen": 0, "discard_without_unfreeze_previously_unfrozen": 0}, "tab_stats": {"frozen_daily": 0, "unfrozen_daily": 0}}, "edge_ci": {"num_healthy_browsers_since_failure": 3}, "edge_llm": {"on_device": {"gpu_info": "4318:10243:D3D12 driver version 32.0.15.7680", "shader_fp16_supported": 2}}, "edge_operation_config": {"_meta": {"version": "1.1.30"}, "edge_ai_assistant": {"ntp_config": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/ntp.8d73e3f1.js", "server_host": "https://authint.bcentral.cn"}}, "pdf_config": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/pdf.js"}}, "real_name_auth_config": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/realNameAuth.9b5fc58a.js"}}, "side_pane_config": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/sidepane.d5bcd879.js"}}, "video_page_config": {"www.bilibili.com": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/bilibili.js"}}, "www.youtube.com": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/youtube.js"}}}}, "edge_drop": {"manifest_canary": {"css": "https://edge-consumer-static.azureedge.net/edropstatic/2025/02/25/1/static/css/main.2a8c2845.css", "js": "https://edge-consumer-static.azureedge.net/edropstatic/2025/02/25/1/static/js/main.915ce316.js", "version": 138}, "manifest_stable": {"css": "https://edge-consumer-static.azureedge.net/edropstatic/2025/02/25/1/static/css/main.2a8c2845.css", "js": "https://edge-consumer-static.azureedge.net/edropstatic/2025/02/25/1/static/js/main.915ce316.js", "version": 138}}, "edge_mouse_gesture": {"blocklist": ["https://earth.google.com", "https://www.figma.com", "https://map.baidu.com", "https://maps.gaode.com", "https://app.diagrams.net"]}, "edge_screenshot": {"dynamic_config": {"resources": {"2": {"untrusted_js": "https://edge-consumer-static.azureedge.net/screenshot/2025/index.6bfeeb46d8664a1f.js"}, "3": {"untrusted_js": "https://edge-consumer-static.azureedge.net/screenshot/2025/index.8147d939d9ed09c5.js"}}}}}, "edgel_llm": {"on_device": {"performance_info_version": "138.0.3351.83"}}, "fre": {"has_first_visible_browser_session_completed": true, "oem_bookmarks_set": true}, "gaming_rewards_hva_pitch_notification": {"backfilled": true}, "hardware_acceleration_mode_previous": true, "host_package_checked_on_browser_version": "138.0.7204.101", "identity_combined_status": {"aad": 2, "ad": 1}, "last_pin_migration_on_edge_version": "138.0.3351.83", "last_pin_migration_on_os_version": "10 Version 22H2 (Build 19045.6093)", "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "metrics": {"client_id_hash_key_created": true}, "migration": {"Default": {"migration_attempt": 0, "migration_version": 5}}, "network_time": {"network_time_mapping": {"local": **********828.119, "network": 1752477750245.0, "ticks": 3496596754.0, "uncertainty": 10209113.0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "138.0.3351.83", "model_crash_count": 0, "performance_class": 6, "performance_class_version": "138.0.3351.83"}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAAD9hSQ1zwTtQr3OeJQikGSOEAAAAB4AAABNAGkAYwByAG8AcwBvAGYAdAAgAEUAZABnAGUAAAAQZgAAAAEAACAAAAB5sdl3m+k4qDbaSsBQy+331ywJXaG4PXg5XV2aH98o+QAAAAAOgAAAAAIAACAAAADyaj/sv4/3FhCbw8EuY8T3ym63iehWwu2VcShm47OyezAAAACdPJEO010YVLtgpQAeSgksCtR8UXoYjIvmxMuc3XkvxWcoNQDO8eLwQYAw6GtRiQFAAAAAAwHD7t7KOOTkSiWLowOm7VFNSNWH4mRi08BQKOC4q7lY+l9dPzW6NwSyUFdbdRF6fLMzKNEWF9QrtGqh+2L6vA=="}, "performance_intervention": {"last_daily_sample": "*****************"}, "policy": {"last_statistics_update": "*****************"}, "privacy_budget": {"meta_experiment_activation_salt": 0.****************}, "profile": {"info_cache": {"Default": {"active_time": **********.054543, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_20", "background_apps": false, "edge_account_cid": "5a87b2492eaa56e6", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "<PERSON>", "edge_account_last_name": "<PERSON>", "edge_account_oid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_force_signout_restore_info": {}, "edge_force_signout_state": 0, "edge_muid": "26D34993E00D6E3016FA5FBAE1576F64", "edge_previously_signin_user_name": "", "edge_profile_can_be_deleted": true, "edge_signed_in_default_name": ********, "edge_test_on_premises": false, "edge_wam_aad_for_app_account_type": 0, "edge_was_previously_signin": false, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "000600001C1980E4", "gaia_name": "<PERSON>", "is_consented_primary_account": true, "is_ephemeral": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "Profile 1", "sign_in_source": 19, "signin.with_credential_provider": false, "user_name": "<EMAIL>"}}, "last_active_profiles": ["<PERSON><PERSON><PERSON>"], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "profiles": {"collect_potential_implicit_signin_data_started": true, "current_profile_name": "", "edge": {"guided_switch_pref": [], "multiple_profiles_with_same_account": false}, "edge_sso_info": {"aad_sso_algo_state": 1, "first_profile_key": "<PERSON><PERSON><PERSON>", "msa_first_profile_key": "<PERSON><PERSON><PERSON>", "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "signin_last_seen_version": "138.0.3351.83", "signin_last_updated_time": **********.654892}, "segmentation_platform": {"segment_execution_result_local_state": {"edge_user_topic_on_url_protobuf": {"execution_time": "*****************", "is_ready": true, "output": [1.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 2.0, 3.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 0.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "segment_id": 522}}}, "sentinel_creation_time": "0", "session_id_generator_last_value": "**********", "signin": {"active_accounts": {"1GStsDgYGrbOgdK29iIu7svKYul57HucHyAjRq0Nam0=": "*****************"}, "active_accounts_last_emitted": "*****************"}, "smartscreen": {"enabled": true, "pua_protection_enabled": true}, "startup_boost": {"last_browser_open_time": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_proactive": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 3, "reloads_external": 0, "reloads_urgent": 0, "total_tab_count_max": 3, "window_count_max": 1}, "telemetry_client": {"cloned_install": {"user_data_dir_id": 8045446}, "governance": {"last_dma_change_date": "*****************", "last_known_cps": 2048}, "host_telclient_path": "QzpcUHJvZ3JhbSBGaWxlcyAoeDg2KVxNaWNyb3NvZnRcRWRnZVxBcHBsaWNhdGlvblwxMzguMC4zMzUxLjgzXHRlbGNsaWVudC5kbGw=", "install_source_name": "windows", "os_integration_level": 5, "sample_id": ********, "updater_version": "**********", "windows_update_applied": false}, "ukm": {"persisted_logs": []}, "uninstall_metrics": {"installation_date2": "1738983995"}, "user_experience_metrics": {"client_id2": "d194c343-e16a-49e0-9283-81862800a09e", "client_id_timestamp": "1738983995", "diagnostics": {"last_data_collection_level_on_launch": 3}, "initial_logs2": [{"count_id": "", "data": "H4sIAAAAAAAAAOP6yMaZYGh+1M2iwEeAQeoyG0fTh92H2YQEDI0t9Az0jI1NDfUsjHXNTCQ2TJy9l02JNTVPNzRYS4KLKzwzLyW/vFjBL0SIy9BAz0DP0NLAxNQom4utwsIs3sxE4G2dREPDg0XT/ssrsfsG65q7mphoMBo08Fvs4HDi4mAQYJBiMGKwYvCtTljsFKrqudgplYGhwT5LgovHPTWvNDMv1TOvJDVH4GuPigSPAoMGQxMjQxcjwypGqPkbGBm9uHiFnzAYisblX7rhxcUbqWS7DsYWZxdZCWNPzRPfAmML35odA2MXSF86C2NPeLnaHswOYIli4mBIYvH0c4rI4gA504LBgaGBEez9GYwMPxgfCDcw/YcBxg4mxhVME1U3MS1gTJGzvDQt5Pf/eacDbM0fra072sGgVXdiQ87t2cYrufv36t84oLvxX/T5drcSE6t3Wbr1WnU6RyV5bnP6bAkR9vwr/1+AwWZyiHzvp1OpddLJ5XK1ld6rMuV43Hof//bT2zHL7vjzHNOkH4s69PNvOiaecHD5t1Wg/dK1Y1zLpL+yfrTkvrtI3aXZU3cP9+zGsL1Jya9z7wbP33fe6cHj1y3dO/77fOaN/chk2P/ppl3BpZRTdq+YuFhAnhPiUGLhYBJg0mBUYuJgMmLjYFBi0GKw4ub4/P//++9gHzppCnEZ6hnrGVqa6pkZSrAqMGjxBaWme6dW+uWXuOWX5qUYsZdDEkMQEwdD1B9GTrdtn/9eqQhzEJRvfR24456DgyQDGETiZnywR2cwMKCL4NFOHgNuBaYz8DjMBLfiSIcqJg6GJkYmCYYuRiYOxlWMTFwMDxiZXjAynrRjhNqpNY2dq4F9NNuNZrvRbEfDbGfEyxnme+TyA9lYHSkmAUb0XCjQwC7BoMTDwSjAIMEASugWDF6/RivD0Vw5mivpmCsR+REA0QX7/4oKAAA=", "hash": "mL+dpple3S0Q9ndVrb/mKXSPNSQ=", "signature": "bf9UGX/q6jbe3avnTrEjk7S5MeoxyK2nS3Hm2D0LWmU=", "timestamp": "1752478449"}, {"count_id": "", "data": "H4sIAAAAAAAAAOP6yMaZYGh+1M2iwEeAUeoyG0fTh92H2YQEDI0t9Az0jI1NDfUsjHXNTCQ2TJy9l02JNTVPNzRYS4KLKzwzLyW/vFjBL0SIy9BAz0DP0NLAxNQom4utwsIs3sxE4G2dREPDg0XT/ssrsfsG65q7mphoMBo08Fvs4HDi4mAQYJBiMGKwYvCtTljsFKrqudgplYGhwT5LgovHPTWvNDMv1TOvJDVH4GuPigSPAoMGQxMjQxcjwypGqPkbGBm9uHiFnzAYisblX7rhxcUbqWS7DsYWZxdZCWNPzRPfAmML35odA2MXSF86C2NPeLnaHswOYIli4mBIYvH0c4rI4gA504LBgaGBEez9GYwMPxgfCDcw/YcBxg4mxhVME1U3MS1gTJGzvDQt5Pf/eacDbM0fra072sGgVXdiQ87t2cYrufv36t84oLvxX/T5drcSE6t3Wbr1WnU6RyV5bnP6bAkR9vwr/1+AwWZyiHzvp1OpddLJ5XK1ld6rMuV43Hof//bT2zHL7vjzHNOkH4s69PNvOiaecHD5t1Wg/dK1Y1zLpL+yfrTkvrtI3aXZU3cP9+zGsL1Jya9z7wbP33fe6cHj1y3dO/77fOaN/chkE2jtOeeHqv0pu1dMXCwgzwlxKLFwMAkwaTAqMXEwGbFxMCgxaDFYcXN8/v///XewD500hbgM9Yz1DC1N9cwMJVgVGLT4glLTvVMr/fJL3PJL81KM2MshiSGIiYMh6g8jp9u2z3+vVIQ5CMq3vg7ccc/BQZIBDCJxMz7YozMYGNBF8GgnjwG3AtMZeBxmgltxpEMVEwdDEyOTBEMXIxMH4ypGJi6GB4wsLxiZTtoxMjAwgLBWOzvXaLYbzXaj2Y6u2U7gI5sEgxIPKPglGEAp24IBALrNH9D+BgAA", "hash": "DTo3yN+3ufOwhcfZHFSXf7QhBbs=", "signature": "6Rp253LSnSCrer2vV3UDtAElirw0yCqiX0asCC3xikY=", "timestamp": "1752478723"}, {"count_id": "", "data": "H4sIAAAAAAAAAOP6yMaZYGh+1M2iwEeASeoyG0fTh92H2YQEDI0t9Az0jI1NDfUsjHXNTCQ2TJy9l02JNTVPNzRYS4KLKzwzLyW/vFjBL0SIy9BAz0DP0NLAxNQom4utwsIs3sxE4G2dREPDg0XT/ssrsfsG65q7mphoMBo08Fvs4HDi4mAQYJBiMGKwYvCtTljsFKrqudgplYGhwT5LgovHPTWvNDMv1TOvJDVH4GuPigSPAoMGQxMjQxcjwypGqPkbGBm9uHiFnzAYisblX7rhxcUbqWS7DsYWZxdZCWNPzRPfAmML35odA2MXSF86C2NPeLnaHswOYIli4mBIYvH0c4rI4gA504LBgaGBEez9GYwMPxgfCDcw/YcBxg4mxhVME1U3MS1gTJGzvDQt5Pf/eacDbM0fra072sGgVXdiQ87t2cYrufv36t84oLvxX/T5drcSE6t3Wbr1WnU6RyV5bnP6bAkR9vwr/1+AwWZyiHzvp1OpddLJ5XK1ld6rMuV43Hof//bT2zHL7vjzHNOkH4s69PNvOiaecHD5t1Wg/dK1Y1zLpL+yfrTkvrtI3aXZU3cP9+zGsL1Jya9z7wbP33fe6cHj1y3dO/77fOaN/cjU32vaI/vVZNopu1dMXCwgzwlxKLFwMAkwaTAqMXEwG7FxMCgxaDFYcXN8/v///XewD500hbgM9Yz1DC1N9cwMJVgVGLT4glLTvVMr/fJL3PJL81KM2MshiSGIiYMh6g8jp9u2z3+vVIQ5CMq3vg7ccc/BQZIBDCJxMz7YozMYGNBF8GgnjwG3AtMZeBxmgltxpEMVEwdDEyOTBFMXIxMH4ypGJi6GB4zsLxhZTtoxMjAwMDEwMGhNY+dqYB/NdqPZbjTb0TDbGfFyhvkeufxANlZHikmAET0XCjSwSzAo8XAwCjBIMIASugWD16/RynA0V47mSjrmSkR+BABRx5cnigoAAA==", "hash": "ZlQ9tz5pKJWmQo/or0IdADZzIzo=", "signature": "TNlVZwD19v80YX2kFl6PNGTTbQAtBBOjshNE5VemgL4=", "timestamp": "1752480103"}, {"count_id": "", "data": "H4sIAAAAAAAAAON6ysaZYGh+1M2iwEeAWeo4G0fTh92H2YQEDI0t9Az0jI1NDfUsjHXNTCQ2TJy9l02JNTVPNzRYS4KLKzwzLyW/vFjBL0SIy9BAz0DP0NLAxNQom4utwsIs3sxE4G2dREPDg0XT/ssrsfsG65q7mphoMBo08Fvs4HDi4mAQYJBiMGKwYvCtTljsFKrqudgplYGhwT5LgovHPTWvNDMv1TOvJDVH4GuPigSPAoMGQxMjQxcjwypGqPkbGBm9uHiFnzAYisblX7rhxcUbqWS7DsYWZxdZCWNPzRPfAmML35odA2NPeLnaHswOYIli4mBIYvH0c4rI4gA5zYLBgaGBEezlGYwMPxgfCDcw/YcBxg4mxhVME1U3MS1gTJGzvDQt5Pf/eacDbM0fra072sGgVXdiQ87t2cYrufv36t84oLvxX/T5drcSE6t3Wbr1WnU6RyV5bnP6bAkR9vwr/1+AwWZyiHzvp1OpddLJ5XK1ld6rMuV43Hof//bT2zHL7vjzHNOkH4s69PNvOiaecHD5t1Wg/dK1Y1zLpL+yfrTkvrtI3aXZU3cP9+zGsL1Jya9z7wbP33fe6cHj1y3dO/77fOaN/ci06bPv66fLEvNO2b1i4mIBeU6IQ4mFg0mASYNRiYmD2YiNg0GJQYvBipvj8///77+DfeikKcRlqGesZ2hpqmdmKMGqwKDFF5Sa7p1a6Zdf4pZfmpdixF4OSQBBTBwMUX8YOd22ff57pSLMQVC+9XXgjnsODpIMYBCJm/HBHp3BwIAugkc7eQy4FZjOwOMwE9yKIx2qmDgYmhiZJJi6GJk4GFcxMnExPGDkesHIdtKOkYGBgZmBgUGri53ry2hWG81qo1mN2lnNiJczzPfI5QeysTpSTAKM6DlP4AubBIMSDwejAIMEAyhxWzB4vRvNiaM5cTQn0jgnIvIgAJH/4AFmCgAA", "hash": "+MB+uDnILNcI7uerG1AfgQowmo8=", "signature": "ca50hofoFLCGCs8ruhktfKpiWvyd//4U0PVomdrpI5A=", "timestamp": "1752480877"}], "last_seen": {"BrowserMetrics": "13396954474439491", "CrashpadMetrics": "13396953709477418"}, "lbfg_date": "1752480879", "limited_entropy_randomization_source": "28F47DC70A7CBB6413F157E0396DF1DB", "log_finalized_record_id": 7, "log_record_id": 12, "low_entropy_source3": 2528, "machine_id": 8621440, "ongoing_logs2": [], "payload_counter": 1, "pseudo_low_entropy_source": 4753, "reporting_enabled": true, "session_id": 4, "stability": {"browser_last_live_timestamp": "13396954477923480", "exited_cleanly": true, "saved_system_profile": "CILwu8MGEhAxMzguMC4zMzUxLjgzLTY0GLCRm70GIgVlbi1VUyoYCgpXaW5kb3dzIE5UEgoxMC4wLjE5MDQ1MmsKBng4Nl82NBDtfhiAgOCilv8fIgdNUy03RTQ0KAEwgA84uAhCCggAEAAaADIAOgBNe2CjQlUlSaNCZQAAgD9qGAoMR2VudWluZUludGVsEPWMJBgMIAAoAIIBAIoBAKoBBng4Nl82NLABAUoKDRPkADEVXm/S2EoKDVkiPa4VXm/S2EoKDRcHFKkVXm/S2EoKDZVuF7QVXm/S2EoKDRPam1wVXm/S2EoKDZDpqz8VXm/S2FAEWgIIAGIESU5CWGoICAAQADgAQACAAbCRm70GmAEA+AHgE4AC////////////AYgCAagCkSWyAqABZB450pZU+/+ey1A9N+KtfsWIACp+yLBs25szqQuPvS/YwC2x/lvPh0Z0NDruai1/Kn4sxRkM2wlMtFQTSf0f/xAAPJNUH43yymV+G2N3Hn15S6ppHgxGjeP7Ti64mj7H52w1YviiiC9v2UFhyEBE/rUQh9LWxgqmG/UF8TkL3aInRINJLbwLm4FWvWJj623dU5++z0Lg4+uEi7j/TPMNXfEC9jlxDxA8/hPKPuUCCgQIABAAEggiBAgCEAIoASICCAIyBggAIgAqADoLCPP//+/3/////wFCKRIKMS4zLjE5NS42MRgFIAAqDlJlZ0tleU5vdEZvdW5kMgd3aW5kb3dzUgIIAFr8AQlGtvP91HhWQBEfhetRuN5AQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAADwPxkAAAAAAADwPxkAAAAAAAAAABkAAAAAAADwPxkAAAAAAABZQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAABZQBkAAAAAAAAAABkAAAAAAABZQBkAAAAAAADwPxkAAAAAAABZQBkAAAAAAADwPxkAAAAAAADwPxkAAAAAAAA0QBkAAAAAAADwPxkAAAAAAABZQBkAAAAAAABZQHoCCACCAQIYAqoBAgoA", "saved_system_profile_hash": "D207CE2E761E38B42E4A504678C4C0F1379A4AD0", "stats_buildtime": "1752102914", "stats_version": "138.0.3351.83-64", "system_crash_count": 0}, "unsent_log_metadata": {"initial_logs": {"sent_samples_count": 0, "unsent_persisted_size_in_kb": 3, "unsent_samples_count": 1}, "ongoing_logs": {"sent_samples_count": 0, "unsent_persisted_size_in_kb": 0, "unsent_samples_count": 0}}}, "variations_google_groups": {"Default": []}, "variations_limited_entropy_synthetic_trial_seed_v2": "40", "was": {"restarted": false}}