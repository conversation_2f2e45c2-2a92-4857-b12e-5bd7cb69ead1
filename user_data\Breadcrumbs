0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Normal
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 AddInfobar73
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab2 StartNav2 #auto_toplevel
0:00:00 Browser1 Insert active Tab2 at 1
0:00:00 Tab2 AddInfobar73
0:00:00 Tab2 FinishNav2
0:00:00 Tab2 PageLoad
0:00:00 Tab2 StartNav3 #typed
0:00:05 Tab2 RemoveInfobar73
0:00:05 Tab2 FinishNav3
0:00:05 Tab2 PageLoad
0:00:05 Tab2 StartNav4 #renderer-script #link
0:00:05 CloseTab_NoAlertIndicator
0:00:05 Microsoft.Shutdown.TabStripModel.CloseWebContentsAt
0:00:05 Microsoft.Shutdown.TabStripModel.CloseTabs
0:00:05 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:00:05 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:00:05 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NotClosingAll
0:00:05 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:00:05 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:00:05 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:00:05 Tab1 RemoveInfobar73
0:00:05 Tab1 RenderProcessGone
0:00:05 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:00:05 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:00:05 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:00:05 Browser1 Close Tab1 at 0
0:00:05 Tab1 WebContentsDestroyed
0:00:05 Tab2 AddInfobar73
0:00:05 Tab2 FinishNav4
0:00:05 Tab2 PageLoad
0:00:05 Tab2 StartNav5 #renderer-script #form_submit
0:00:05 Microsoft.ENP.Framework.CourtesyEngine.FrequencyCappingAllowed
0:00:05 Microsoft.Nurturing.Framework.CourtesyEngine.FrequencyCappingAllowed
0:00:06 Widget Closed: BubbleDialogDelegateView
0:00:07 Tab2 FinishNav5
0:00:08 Tab2 StartNav6 #renderer-user #auto_subframe
0:00:10 Tab2 FinishNav6
0:00:10 Tab2 PageLoad
0:00:10 Tab2 StartNav7 #renderer-script #auto_subframe
0:00:10 Tab2 FinishNav7
0:00:10 Tab2 PageLoad
0:00:10 Tab2 StartNav8 #renderer-script #auto_subframe
0:00:11 Tab2 FinishNav8
0:00:11 Tab2 PageLoad
0:00:11 Tab2 StartNav9 #renderer-script #auto_subframe
0:00:11 Tab2 PageLoad
0:00:11 Tab2 FinishNav9
0:00:11 Tab2 PageLoad
0:00:36 Widget Closed: StatusBubble
0:01:40 Tab2 StartNav10 #typed
0:01:41 Tab2 RemoveInfobar73
0:01:41 Tab2 FinishNav10
0:01:41 Tab2 AddInfobar73
0:01:41 Tab2 StartNav11 #renderer-script #auto_subframe
0:01:42 Tab2 FinishNav11
0:01:42 Tab2 PageLoad
0:01:42 Tab2 PageLoad
0:02:47 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
