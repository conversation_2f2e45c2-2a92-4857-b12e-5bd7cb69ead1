0:07:35 Tab2 RemoveInfobar73
0:07:35 Tab2 FinishNav87
0:07:35 Tab2 AddInfobar73
0:07:35 Tab2 PageLoad #bing
0:07:42 Tab2 StartNav88 #bing #typed
0:07:42 Tab2 RemoveInfobar73
0:07:42 Tab2 FinishNav88
0:07:42 Tab2 PageLoad #bing
0:07:42 Tab2 AddInfobar73
0:07:42 Tab2 StartNav89 #bing #renderer-user #form_submit
0:07:43 Tab2 RemoveInfobar73
0:07:43 Tab2 FinishNav89
0:07:43 Tab2 AddInfobar73
0:07:43 Tab2 PageLoad #bing
0:07:51 Tab2 StartNav90 #bing #typed
0:07:51 Tab2 RemoveInfobar73
0:07:51 Tab2 FinishNav90
0:07:51 Tab2 PageLoad #bing
0:07:51 Tab2 AddInfobar73
0:07:51 Tab2 StartNav91 #bing #renderer-user #form_submit
0:07:51 Tab2 RemoveInfobar73
0:07:51 Tab2 FinishNav91
0:07:51 Tab2 AddInfobar73
0:07:51 Tab2 PageLoad #bing
0:07:58 Tab2 StartNav92 #typed
0:07:59 Tab2 RemoveInfobar73
0:07:59 Tab2 FinishNav92
0:07:59 Tab2 StartNav93 #renderer-script #auto_subframe
0:07:59 Tab2 AddInfobar73
0:08:00 Tab2 FinishNav93
0:08:00 Tab2 PageLoad
0:08:00 Tab2 PageLoad
0:08:00 Tab2 StartNav94 #renderer-script #auto_subframe
0:08:00 Tab2 FinishNav94
0:08:00 Tab2 PageLoad
0:08:15 Tab2 StartNav95 #typed
0:08:16 Tab2 RemoveInfobar73
0:08:16 Tab2 FinishNav95
0:08:16 Tab2 StartNav96 #renderer-script #auto_subframe
0:08:16 Tab2 AddInfobar73
0:08:16 Tab2 FinishNav96
0:08:16 Tab2 PageLoad
0:08:16 Tab2 PageLoad
0:08:31 Tab2 StartNav97 #typed
0:08:32 Tab2 RemoveInfobar73
0:08:32 Tab2 FinishNav97
0:08:32 Tab2 StartNav98 #renderer-script #auto_subframe
0:08:32 Tab2 AddInfobar73
0:08:32 Tab2 FinishNav98
0:08:32 Tab2 PageLoad
0:08:32 Tab2 PageLoad
0:08:37 Microsoft.Shutdown.ExitIgnoreUnloadHandlers
0:08:37 Tab1 RemoveInfobar73
0:08:37 Tab1 RenderProcessGone
0:08:37 Microsoft.Shutdown.OnShutdownStarting
0:08:37 Microsoft.Shutdown.SetTryingToQuit_Quitting
0:08:37 Microsoft.Shutdown.OnClosingAllBrowsers_Closing
0:08:37 Microsoft.Shutdown.AttemptExit_QuitApplication
0:08:37 Microsoft.Shutdown.CloseAllBrowsers
0:08:37 Microsoft.Shutdown.StartClosingBrowsers
0:08:37 Microsoft.Shutdown.SetTryingToQuit_Quitting
0:08:37 Microsoft.Shutdown.CloseBrowsers_IgnoreUnloadHandlers
0:08:37 Microsoft.Shutdown.OnWindowClosing
0:08:37 Microsoft.Shutdown.OnWindowClosing_MaybeClearBrowsingDataOnExit
0:08:37 Microsoft.Shutdown.OnWindowClosing_ClearBrowsingDataOnExitNotInProgress
0:08:37 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:08:37 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_ShouldStartShutdown
0:08:37 Microsoft.Shutdown.TabStripModel.CloseAllTabs
0:08:37 Microsoft.Shutdown.TabStripModel.CloseTabs
0:08:37 Microsoft.Shutdown.TabStripModel.CloseTabs_EdgeBeforeClosingAllTabs
0:08:37 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:08:37 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:08:37 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:08:37 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosingAll
0:08:37 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:08:37 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:08:37 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:08:37 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:08:37 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:08:37 Browser1 Closed 2 tabs
0:08:37 Tab2 RemoveInfobar73 #not-animated
0:08:37 Tab2 WebContentsDestroyed
0:08:37 Tab1 WebContentsDestroyed
0:08:37 Microsoft.UnloadController.TabStripEmpty
0:08:37 Microsoft.Shutdown.OnWindowClosing
0:08:37 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:08:37 Widget Closed: BrowserFrame
0:08:37 Microsoft.Shutdown.TabStripModel.CloseAllTabs
0:08:37 Microsoft.Shutdown.TabStripModel.CloseTabs
0:08:37 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:08:37 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NoTabsClosable
0:08:37 Widget Closed: BrowserFrame
0:08:37 Microsoft.Last_Browser_Removed
0:08:37 Microsoft.Shutdown.NotifyAppTerminating
0:08:37 Microsoft.Shutdown.OnAppExiting
0:08:37 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura
0:08:37 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.NotificationUIManager_StartShutdown
0:08:37 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.CloseAllSecondaryWidgets
0:08:38 Shutdown
