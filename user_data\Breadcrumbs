0:00:08 Microsoft.Shutdown.TabStripModel.CloseTabs
0:00:08 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:00:08 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:00:08 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NotClosingAll
0:00:08 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:00:08 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:00:08 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:00:08 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:00:08 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:00:08 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:00:08 Browser1 Close Tab1 at 0
0:00:08 Tab1 RemoveInfobar73 #not-animated
0:00:08 Tab1 WebContentsDestroyed
0:00:09 Widget Closed: TooltipAura
0:00:09 CloseTab_NoAlertIndicator
0:00:09 Microsoft.Shutdown.TabStripModel.CloseWebContentsAt
0:00:09 Microsoft.Shutdown.TabStripModel.CloseTabs
0:00:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:00:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:00:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NotClosingAll
0:00:09 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:00:09 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:00:09 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:00:09 Tab2 RemoveInfobar73
0:00:09 Tab2 RenderProcessGone
0:00:09 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:00:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:00:09 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:00:09 Browser1 Close Tab2 at 0
0:00:09 Tab2 WebContentsDestroyed
0:00:20 Widget Closed: StatusBubble
