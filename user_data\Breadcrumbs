0:00:08 Microsoft.Shutdown.TabStripModel.CloseTabs
0:00:08 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:00:08 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:00:08 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NotClosingAll
0:00:08 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:00:08 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:00:08 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:00:08 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:00:08 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:00:08 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:00:08 Browser1 Close Tab1 at 0
0:00:08 Tab1 RemoveInfobar73 #not-animated
0:00:08 Tab1 WebContentsDestroyed
0:00:09 Widget Closed: TooltipAura
0:00:09 CloseTab_NoAlertIndicator
0:00:09 Microsoft.Shutdown.TabStripModel.CloseWebContentsAt
0:00:09 Microsoft.Shutdown.TabStripModel.CloseTabs
0:00:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:00:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:00:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_NotClosingAll
0:00:09 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:00:09 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:00:09 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:00:09 Tab2 RemoveInfobar73
0:00:09 Tab2 RenderProcessGone
0:00:09 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:00:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:00:09 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:00:09 Browser1 Close Tab2 at 0
0:00:09 Tab2 WebContentsDestroyed
0:00:20 Widget Closed: StatusBubble
0:01:04 Microsoft.Shutdown.ExitIgnoreUnloadHandlers
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Normal
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 AddInfobar73
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab2 StartNav2 #auto_toplevel
0:00:00 Browser1 Insert active Tab2 at 1
0:00:00 Tab2 AddInfobar73
0:00:00 Tab2 FinishNav2
0:00:00 Tab2 PageLoad
0:00:00 Tab2 StartNav3 #typed
0:00:00 Tab2 RemoveInfobar73
0:00:00 Tab2 FinishNav3
0:00:01 Tab2 AddInfobar73
0:00:01 Tab2 StartNav4 #renderer-script #auto_subframe
0:00:01 Tab2 FinishNav4
0:00:02 Tab2 PageLoad
0:00:02 Tab2 PageLoad
0:00:02 Tab2 StartNav5 #renderer-script #auto_subframe
0:00:02 Tab2 FinishNav5
0:00:02 Tab2 PageLoad
0:00:02 Tab2 StartNav6 #renderer-script #auto_subframe
0:00:02 Tab2 FinishNav6
0:00:02 Tab2 PageLoad
0:00:02 Tab2 StartNav7 #renderer-script #auto_subframe
0:00:02 Widget Closed: BubbleDialogModelHostContentsView
0:00:03 Tab2 FinishNav7
0:00:03 Tab2 PageLoad
0:00:12 Widget Closed: StatusBubble
0:00:40 Tab2 StartNav8 #typed
0:00:41 Tab2 RemoveInfobar73
0:00:41 Tab2 FinishNav8
0:00:41 Tab2 StartNav9 #renderer-script #auto_subframe
0:00:41 Tab2 AddInfobar73
0:00:41 Tab2 FinishNav9
0:00:41 Tab2 PageLoad
0:00:41 Tab2 PageLoad
0:01:45 Microsoft.Shutdown.ExitIgnoreUnloadHandlers
